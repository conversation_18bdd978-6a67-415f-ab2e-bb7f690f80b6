#!/usr/bin/env python3
"""
多重继承初始化BUG修复验证脚本
"""

def verify_inheritance_fix():
    """验证多重继承初始化修复的正确性"""
    
    print("=== 多重继承初始化BUG修复验证 ===\n")
    
    # 验证1：检查修复内容
    print("1. 检查table_labelmev2.py修复内容:")
    
    try:
        with open('lib/datasets/dataset/table_labelmev2.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复是否正确应用
        old_call = "super(Table, self).__init__()"
        new_call = "super(Table, self).__init__(opt, split)"
        
        has_old_call = old_call in content
        has_new_call = new_call in content
        
        print(f"   ❌ 旧的错误调用存在: {has_old_call} (应该为False)")
        print(f"   ✅ 新的正确调用存在: {has_new_call} (应该为True)")
        
        if not has_old_call and has_new_call:
            print("   ✅ super()调用修复成功")
        else:
            print("   ❌ super()调用修复失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 文件检查失败: {e}")
        return False
    
    # 验证2：测试多重继承类创建
    print("\n2. 测试多重继承类创建:")
    
    try:
        import sys
        sys.path.append('.')
        
        # 导入相关类
        from lib.datasets.dataset.table_labelmev2 import Table as Table_labelmev2
        from lib.datasets.sample.table_ctdet import TableLabelMeCTDetDataset
        
        print("   ✅ 基础类导入成功")
        
        # 创建多重继承类（模拟工厂函数）
        class TestTableLabelMeDataset(Table_labelmev2, TableLabelMeCTDetDataset):
            pass
        
        print("   ✅ 多重继承类定义成功")
        print(f"   MRO: {[cls.__name__ for cls in TestTableLabelMeDataset.__mro__]}")
        
    except Exception as e:
        print(f"   ❌ 多重继承类创建失败: {e}")
        return False
    
    # 验证3：测试实例化（模拟配置）
    print("\n3. 测试实例化:")
    
    try:
        # 创建模拟配置对象
        class MockOpt:
            def __init__(self):
                self.dataset_name = 'TableLabelMe'
                self.data_config = '/mock/path/config.py'
                self.config_name = 'test_config'
                self.enable_header_prediction = False
                self.enable_content_prediction = False
                self.enable_border_prediction = False
        
        mock_opt = MockOpt()
        
        # 尝试实例化
        dataset_instance = TestTableLabelMeDataset(mock_opt, 'train')
        print("   ✅ 实例化成功")
        print(f"   实例类型: {type(dataset_instance).__name__}")
        print(f"   split属性: {getattr(dataset_instance, 'split', 'None')}")
        print(f"   opt属性存在: {hasattr(dataset_instance, 'opt')}")
        
        # 检查关键属性
        key_attributes = ['split', 'opt', 'enable_header_prediction', 'logger']
        for attr in key_attributes:
            has_attr = hasattr(dataset_instance, attr)
            print(f"   属性 {attr}: {'✅' if has_attr else '❌'}")
        
    except Exception as e:
        print(f"   ❌ 实例化失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 验证4：测试工厂函数
    print("\n4. 测试工厂函数:")
    
    try:
        from lib.datasets.dataset_factory import _create_tablelabelme_dataset
        
        # 测试工厂函数创建
        DatasetClass = _create_tablelabelme_dataset('ctdet_mid')
        print("   ✅ 工厂函数调用成功")
        print(f"   返回类名: {DatasetClass.__name__}")
        
        # 测试工厂函数创建的类实例化
        factory_instance = DatasetClass(mock_opt, 'val')
        print("   ✅ 工厂函数创建的类实例化成功")
        print(f"   实例类型: {type(factory_instance).__name__}")
        
    except Exception as e:
        print(f"   ❌ 工厂函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n=== 验证总结 ===")
    print("✅ super()调用修复成功")
    print("✅ 多重继承类创建正常")
    print("✅ 实例化过程正常")
    print("✅ 工厂函数工作正常")
    print("✅ 多重继承初始化BUG已修复")
    
    return True

if __name__ == "__main__":
    success = verify_inheritance_fix()
    if success:
        print("\n🎉 BUG修复验证通过！现在可以进行端到端训练验证了。")
    else:
        print("\n❌ BUG修复验证失败，需要进一步检查。")
