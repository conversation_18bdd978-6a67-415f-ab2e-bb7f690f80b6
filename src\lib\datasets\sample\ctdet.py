from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import torch.utils.data as data
import numpy as np
import torch
import json
import cv2
import os
from utils.image import flip, color_aug
from utils.image import get_affine_transform, affine_transform, get_affine_transform_upper_left
from utils.post_process import ctdet_4ps_post_process
from utils.image import gaussian_radius, draw_umich_gaussian, draw_umich_gaussian_wh, draw_msra_gaussian
from utils.image import draw_dense_reg
from utils.adjacency import adjacency, h_adjacency, v_adjacency, same_col, same_row
import math
import time
import random
import imgaug.augmenters as iaa
import time 

class CTDetDataset(data.Dataset):
  def _coco_box_to_bbox(self, box):
    bbox = np.array([box[0], box[1], box[0] + box[2], box[1] + box[3]],
                    dtype=np.float32)
    return bbox

  def _get_border(self, border, size):
    i = 1
    while size - border // i <= border // i:
        i *= 2
    return border // i

  def _get_border_upper_left(self, border, size):
    i = 1
    while size/2 - border // i <= border // i:
        i *= 2
    return border // i

  def _get_radius(self,r,w,h):
    if w > h:
        k = float(w)/float(h)
    else:
        k = float(h)/float(w)
    ratio = k**0.5
    if w>h:
        r_w = r*ratio
        r_h = r
    else:
        r_h = r*ratio
        r_w = r
    return int(r_w),int(r_h)

  def color(self,image,p,magnitude):
    if np.random.randint(0,10) > p*10:
      return image
    gray_img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    bgr_img = cv2.cvtColor(gray_img, cv2.COLOR_GRAY2BGR)
    img_float,bgr_img_float = img.astype(float), bgr_img.astype(float)
    diff = img_float - bgr_img_float
    diff = diff*magnitude
    diff_img_ = diff + bgr_img_float
    diff_img_ = diff_img_.astype(np.uint8)
    diff_img_ = np.array(diff_img_)
    diff_img_ = np.clip(diff_img_,0,255)
    diff_img_ = cv2.cvtColor(diff_img_,cv2.COLOR_BGR2RGB)
    diff_img_ = cv2.cvtColor(diff_img_,cv2.COLOR_RGB2BGR)
    return diff_img_

  def rotate(self,p,magnitude):
    if np.random.randint(0,10) > p*10:
      return 0
    rot = np.random.randint(magnitude[0],magnitude[1])
    return rot

  def hisEqulColor(self,img):
    (b, g, r) = cv2.split(img)
    bH = cv2.equalizeHist(b)
    gH = cv2.equalizeHist(g)
    rH = cv2.equalizeHist(r)
    result = cv2.merge((bH, gH, rH))
    return img

  def _judge(self,box):
    countx = len(list(set([box[0],box[2],box[4],box[6]]))) 
    county = len(list(set([box[1],box[3],box[5],box[7]]))) 
    if countx<2 or county<2:
        return False
    
    return True

  def _get_Center(self, point):
    x1 = point[0]
    y1 = point[1]
    x3 = point[2]
    y3 = point[3]
    x2 = point[4]
    y2 = point[5]
    x4 = point[6]
    y4 = point[7]
    w1 = math.sqrt((x1-x3)*(x1-x3)+(y1-y3)*(y1-y3))
    w2 = math.sqrt((x2-x4)*(x2-x4)+(y2-y4)*(y2-y4))
    h1 = math.sqrt((x1-x4)*(x1-x4)+(y1-y4)*(y1-y4))
    h2 = math.sqrt((x2-x3)*(x2-x3)+(y2-y3)*(y2-y3))
    nw = min(w1,w2)
    nh = min(h1,h2)
    x_dev = x4*y2-x4*y1-x3*y2+x3*y1-x2*y4+x2*y3+x1*y4-x1*y3
    y_dev = y4*x2-y4*x1-y3*x2+x1*y3-y2*x4+y2*x3+y1*x4-y1*x3
    c_x = 0
    c_y = 0
    if x_dev != 0:
      c_x = (y3*x4*x2-y4*x3*x2-y3*x4*x1+y4*x3*x1-y1*x2*x4+y2*x1*x4+y1*x2*x3-y2*x1*x3)/x_dev
    if y_dev != 0:
      c_y = (-y3*x4*y2+y4*x3*y2+y3*x4*y1-y4*x3*y1+y1*x2*y4-y1*x2*y3-y2*x1*y4+y2*x1*y3)/y_dev
    return nw,nh,c_x,c_y

  def _rank(self,bbox,cter,file_name):
    init_bbox = bbox
    #bbox = list(map(float,bbox))
    continue_sign = False
    bbox = [bbox[0:2],bbox[2:4],bbox[4:6],bbox[6:8]]
    bbox_= np.array(bbox) - np.array(cter)
    i,box_y,sign= 0,[],'LT'
    choice = []
    for box in bbox_:
        if box[0]<0 and box[1]<0:
            box_y.append(box)
            choice.append(i)
        i = i + 1
    if len(choice)==0: 
        i,box_y,sign = 0,[],'RT'
        for box in bbox_:
            if box[0]>0 and box[1]<0:  
                box_y.append(box)
                choice.append(i)
            i = i + 1
    if sign=='LT':
        ylist = np.array(box_y)[:,1]
        #index = list(ylist).index(max(ylist))  
        index = list(ylist).index(min(ylist))  
    elif sign=='RT':
        try:
            xlist = np.array(box_y)[:,0]
        except Exception as e: 
            print("center:",cter,"box:",init_bbox,"box_y:",box_y)
            return True,bbox
        index = list(xlist).index(min(xlist))  
    
    index = choice[index]
    p = []
    for i in range(4):
        if i + index < 4:
            p.append(bbox[index+i])
        else:
            p.append(bbox[index+i-4])
    return continue_sign,[p[0][0],p[0][1],p[1][0],p[1][1],p[2][0],p[2][1],p[3][0],p[3][1]]

  def __getitem__(self, index):
    img_id = self.images[index]
    file_name = self.coco.loadImgs(ids=[img_id])[0]['file_name']

    # 数据加载日志（Bug修复 - 帮助定位问题数据）
    # if hasattr(self, 'split') and self.split == 'val':
      # 验证阶段详细日志
      # print(f"[ctdet数据加载] 📊 验证样本: index={index}, img_id={img_id}, file={file_name}")
    # elif index % 100 == 0:  # 训练阶段每100个样本记录一次
      # print(f"[ctdet数据加载] 📊 训练样本: index={index}, img_id={img_id}, file={file_name}")
    if self.opt.dataset_name == 'ICDAR19':
      if self.split == 'train':
        img_path = os.path.join(self.img_dir, 'train_images' ,file_name)
      else:
        img_path = os.path.join(self.img_dir, 'test_images' ,file_name)
    else:
      img_path = os.path.join(self.img_dir, file_name)
    ann_ids = self.coco.getAnnIds(imgIds=[img_id])
    anns = self.coco.loadAnns(ids=ann_ids)
    num_objs = min(len(anns), self.max_objs)

    # # 标注数据日志（Bug修复 - 检测异常标注）
    # if hasattr(self, 'split') and self.split == 'val':
    #   print(f"[ctdet标注] 📋 验证样本标注: img_id={img_id}, 标注数={len(anns)}, 处理数={num_objs}")
    #   # 检查每个标注的logic_axis
    #   for i, ann in enumerate(anns[:num_objs]):
    #     if 'logic_axis' in ann:
    #       logic_axis_raw = ann['logic_axis'][0] if ann['logic_axis'] else [0,0,0,0]
    #       print(f"[ctdet标注] 🔍 标注{i}: ann_id={ann.get('id', 'unknown')}, "
    #             f"logic_axis={logic_axis_raw}")
    #     else:
    #       print(f"[ctdet标注] ⚠️  标注{i}: ann_id={ann.get('id', 'unknown')}, 缺少logic_axis字段")
    num_cors = self.max_cors
    if self.opt.dataset_name == 'TG24K':
      img_path = img_path.replace('.jpg', '_org.png')
    elif self.opt.dataset_name == 'SciTSR':
      img_path = img_path.replace('.jpg', '.png')
    elif self.opt.dataset_name == 'PTN':
      img_path = img_path.replace('.jpg', '.png')
    elif self.opt.dataset_name == 'bankdata_june':
      img_path = img_path[:-4]
   
    img = cv2.imread(img_path)
    img_size = img.shape

    height, width = img.shape[0], img.shape[1]

    if self.opt.upper_left:
      c = np.array([0, 0], dtype=np.float32)
    else:
      c = np.array([img.shape[1] / 2., img.shape[0] / 2.], dtype=np.float32)

    if self.opt.keep_res:
      input_h = (height | self.opt.pad)# + 1
      input_w = (width | self.opt.pad)# + 1
      s = np.array([input_w, input_h], dtype=np.float32)
    else:
      s = max(img.shape[0], img.shape[1]) * 1.0
      input_h, input_w = self.opt.input_h, self.opt.input_w
   
    
    flipped = False
    if self.split == 'train':
      if not self.opt.not_rand_crop:
        if self.opt.upper_left:
          c = np.array([0, 0], dtype=np.float32)
        else:
          s = s * np.random.choice(np.arange(0.6, 1.4, 0.1))
          w_border = self._get_border(128, img.shape[1])
          h_border = self._get_border(128, img.shape[0])
          c[0] = np.random.randint(low=w_border, high=img.shape[1] - w_border)
          c[1] = np.random.randint(low=h_border, high=img.shape[0] - h_border)

      else:
        sf = self.opt.scale
        cf = self.opt.shift
        c[0] += s * np.clip(np.random.randn()*cf, -2*cf, 2*cf)
        c[1] += s * np.clip(np.random.randn()*cf, -2*cf, 2*cf)
        s = s * np.clip(np.random.randn()*sf + 1, 1 - sf, 1 + sf)
    
    rot = 0
    if self.opt.rotate==1:
      print('----rotate----')
      rot = np.random.randint(-15,15) 

    output_h = input_h // self.opt.down_ratio
    output_w = input_w // self.opt.down_ratio

    # 安全索引计算函数（Bug修复 - 防止整数溢出）
    def safe_index_calc(y, x, width):
        """
        安全计算二维坐标到一维索引的转换，防止整数溢出

        Args:
            y: y坐标
            x: x坐标
            width: 图像宽度

        Returns:
            安全的索引值，限制在int64范围内
        """
        try:
            # 确保坐标在合理范围内
            y_safe = max(0, min(int(y), output_h - 1))
            x_safe = max(0, min(int(x), output_w - 1))
            width_safe = int(width)

            # 计算索引，检查溢出
            index = y_safe * width_safe + x_safe

            # 检查是否超出int64范围
            MAX_INT64 = 9223372036854775807
            if index > MAX_INT64:
                print(f"[ctdet警告] 🚨 索引计算溢出: y={y}, x={x}, width={width}, index={index}")
                # 使用安全的替代值
                index = y_safe * 1000 + x_safe  # 使用较小的宽度
                print(f"[ctdet修复] 🔧 使用安全索引: {index}")

            return int(index)

        except Exception as e:
            print(f"[ctdet错误] ❌ 索引计算失败: y={y}, x={x}, width={width}, error={e}")
            return 0

    if self.opt.upper_left:
      trans_input = get_affine_transform_upper_left(c, s, rot, [input_w, input_h])
      trans_output = get_affine_transform_upper_left(c, s, rot, [output_w, output_h])
      trans_output_mk = get_affine_transform_upper_left(c, s, rot, [output_w, output_h])
    else:
     
      trans_input = get_affine_transform(c, s, rot, [input_w, input_h])
      trans_output = get_affine_transform(c, s, rot, [output_w, output_h])
      trans_output_mk = get_affine_transform(c, s, rot, [output_w, output_h])
      
    num_classes = self.num_classes
    
    hm = np.zeros((num_classes, output_h, output_w), dtype=np.float32)
    wh = np.zeros((self.max_objs, 8), dtype=np.float32)
    reg = np.zeros((self.max_objs*5, 2), dtype=np.float32)
    st = np.zeros((self.max_cors, 8), dtype=np.float32)
    hm_ctxy = np.zeros((self.max_objs, 2), dtype=np.float32)
    hm_ind = np.zeros((self.max_objs), dtype=np.int64)
    hm_mask = np.zeros((self.max_objs), dtype=np.uint8)
    mk_ind = np.zeros((self.max_cors), dtype=np.int64)
    mk_mask = np.zeros((self.max_cors), dtype=np.uint8)
    reg_ind = np.zeros((self.max_objs*5), dtype=np.int64)
    reg_mask = np.zeros((self.max_objs*5), dtype=np.uint8)
    ctr_cro_ind = np.zeros((self.max_objs*4), dtype=np.int64)
    log_ax = np.zeros((self.max_objs, 4), dtype=np.float32)
    cc_match = np.zeros((self.max_objs, 4), dtype=np.int64)
    h_pair_ind = np.zeros((self.max_pairs), dtype=np.int64)
    v_pair_ind = np.zeros((self.max_pairs), dtype=np.int64)
    draw_gaussian = draw_msra_gaussian if self.opt.mse_loss else \
                    draw_umich_gaussian
    gt_det = []
    corList = []
    point = []
    pair_mark = 0
    inp = cv2.warpAffine(img, trans_input, (input_w, input_h),flags=cv2.INTER_LINEAR)
    
    for k in range(num_objs):
      ann = anns[k]
      
      seg_mask = ann['segmentation'][0] #[[351.0, 73.0, 172.0, 70.0, 174.0, 127.0, 351.0, 129.0, 351.0, 73.0]]
      x1,y1 = seg_mask[0],seg_mask[1]
      x2,y2 = seg_mask[2],seg_mask[3]
      x3,y3 = seg_mask[4],seg_mask[5]
      x4,y4 = seg_mask[6],seg_mask[7]
   
      CorNer = np.array([x1,y1,x2,y2,x3,y3,x4,y4])
      boxes = [[CorNer[0],CorNer[1]],[CorNer[2],CorNer[3]],\
               [CorNer[4],CorNer[5]],[CorNer[6],CorNer[7]]]
      cls_id = int(self.cat_ids[ann['category_id']])

      if flipped:
       
        CorNer[[0,2,4,6]] = width - CorNer[[2,0,6,4]] - 1

      CorNer[0:2] = affine_transform(CorNer[0:2], trans_output_mk)
      CorNer[2:4] = affine_transform(CorNer[2:4], trans_output_mk)
      CorNer[4:6] = affine_transform(CorNer[4:6], trans_output_mk)
      CorNer[6:8] = affine_transform(CorNer[6:8], trans_output_mk)
      CorNer[[0,2,4,6]] = np.clip(CorNer[[0,2,4,6]], 0, output_w - 1)
      CorNer[[1,3,5,7]] = np.clip(CorNer[[1,3,5,7]], 0, output_h - 1)
      if not self._judge(CorNer):
          continue
 
      maxx = max([CorNer[2*I] for I in range(0,4)])
      minx = min([CorNer[2*I] for I in range(0,4)])
      maxy = max([CorNer[2*I+1] for I in range(0,4)])
      miny = min([CorNer[2*I+1] for I in range(0,4)])
      h, w = maxy-miny, maxx-minx #bbox[3] - bbox[1], bbox[2] - bbox[0]
      if h > 0 and w > 0:
       
        radius = gaussian_radius((math.ceil(h), math.ceil(w)))
        radius = max(0, int(radius))
        radius = self.opt.hm_gauss if self.opt.mse_loss else radius

        ct = np.array([(maxx+minx)/2.0,(maxy+miny)/2.0], dtype=np.float32)

        # 安全坐标转换（Bug修复 - 防止坐标溢出）
        try:
            # 检查坐标范围
            if ct[0] < -1000000 or ct[0] > 1000000 or ct[1] < -1000000 or ct[1] > 1000000:
                print(f"[ctdet警告] 🚨 检测到异常中心坐标: ct={ct}, img_id={img_id}")
                # 截断到合理范围
                ct[0] = max(-1000000, min(1000000, ct[0]))
                ct[1] = max(-1000000, min(1000000, ct[1]))
                print(f"[ctdet修复] 🔧 已截断中心坐标: ct={ct}")

            ct_int = ct.astype(np.int32)

            # 进一步检查转换后的整数坐标
            if ct_int[0] < 0 or ct_int[0] >= output_w or ct_int[1] < 0 or ct_int[1] >= output_h:
                print(f"[ctdet警告] ⚠️  中心坐标超出输出范围: ct_int={ct_int}, "
                      f"output_size=({output_w}, {output_h}), img_id={img_id}")
                # 截断到输出范围内
                ct_int[0] = max(0, min(output_w - 1, ct_int[0]))
                ct_int[1] = max(0, min(output_h - 1, ct_int[1]))
                print(f"[ctdet修复] 🔧 已截断到输出范围: ct_int={ct_int}")

        except Exception as e:
            print(f"[ctdet错误] ❌ 中心坐标处理失败: {e}, 使用默认值")
            ct_int = np.array([output_w//2, output_h//2], dtype=np.int32)
       
        draw_gaussian(hm[cls_id], ct_int, radius)

        for i in range(4):
          Cor = np.array([CorNer[2*i],CorNer[2*i+1]], dtype=np.float32)

          # 安全角点坐标转换（Bug修复 - 防止坐标溢出）
          try:
              # 检查角点坐标范围
              if Cor[0] < -1000000 or Cor[0] > 1000000 or Cor[1] < -1000000 or Cor[1] > 1000000:
                  print(f"[ctdet警告] 🚨 检测到异常角点坐标: Cor={Cor}, corner={i}, img_id={img_id}")
                  # 截断到合理范围
                  Cor[0] = max(-1000000, min(1000000, Cor[0]))
                  Cor[1] = max(-1000000, min(1000000, Cor[1]))
                  print(f"[ctdet修复] 🔧 已截断角点坐标: Cor={Cor}")

              Cor_int = Cor.astype(np.int32)

              # 进一步检查转换后的整数坐标
              if Cor_int[0] < -10000 or Cor_int[0] > 10000 or Cor_int[1] < -10000 or Cor_int[1] > 10000:
                  print(f"[ctdet警告] ⚠️  角点坐标超出合理范围: Cor_int={Cor_int}, "
                        f"corner={i}, img_id={img_id}")
                  # 截断到合理范围
                  Cor_int[0] = max(-10000, min(10000, Cor_int[0]))
                  Cor_int[1] = max(-10000, min(10000, Cor_int[1]))
                  print(f"[ctdet修复] 🔧 已截断角点到合理范围: Cor_int={Cor_int}")

          except Exception as e:
              print(f"[ctdet错误] ❌ 角点坐标处理失败: {e}, corner={i}, 使用默认值")
              Cor_int = np.array([0, 0], dtype=np.int32)
          Cor_key = str(Cor_int[0])+"_"+str(Cor_int[1])
          if Cor_key not in corList:
            
            corNum = len(corList)
            
            corList.append(Cor_key)
            reg[self.max_objs+corNum] = np.array([abs(Cor[0]-Cor_int[0]),abs(Cor[1]-Cor_int[1])])
            # 安全索引计算（Bug修复 - 防止整数溢出）
            mk_ind[corNum] = safe_index_calc(Cor_int[1], Cor_int[0], output_w)
            cc_match[k][i] = mk_ind[corNum]
            reg_ind[self.max_objs+corNum] = safe_index_calc(Cor_int[1], Cor_int[0], output_w)
            mk_mask[corNum] = 1
            reg_mask[self.max_objs+corNum] = 1
            draw_gaussian(hm[num_classes-1], Cor_int, 2)
            st[corNum][i*2:(i+1)*2] = np.array([Cor[0]-ct[0],Cor[1]-ct[1]])
            ctr_cro_ind[4*k+i] = corNum*4 + i
        
          else:
            index_of_key = corList.index(Cor_key)
            cc_match[k][i] = mk_ind[index_of_key]
            st[index_of_key][i*2:(i+1)*2] = np.array([Cor[0]-ct[0],Cor[1]-ct[1]])
            ctr_cro_ind[4*k+i] = index_of_key*4 + i
            
        wh[k] = ct[0] - 1. * CorNer[0], ct[1] - 1. * CorNer[1], \
                ct[0] - 1. * CorNer[2], ct[1] - 1. * CorNer[3], \
                ct[0] - 1. * CorNer[4], ct[1] - 1. * CorNer[5], \
                ct[0] - 1. * CorNer[6], ct[1] - 1. * CorNer[7]
        
        # 安全索引计算（Bug修复 - 防止整数溢出）
        hm_ind[k] = safe_index_calc(ct_int[1], ct_int[0], output_w)
        hm_mask[k] = 1
        reg_ind[k] = safe_index_calc(ct_int[1], ct_int[0], output_w)
        reg_mask[k] = 1
        reg[k] = ct - ct_int
        hm_ctxy[k] = ct[0],ct[1]

        # 安全处理logic_axis，防止整数溢出（Bug修复）
        try:
            # 提取logic_axis原始值
            logic_axis_raw = ann['logic_axis'][0]
            raw_values = [logic_axis_raw[0], logic_axis_raw[1], logic_axis_raw[2], logic_axis_raw[3]]

            # 定义安全范围
            MIN_SAFE_VALUE = -2147483648  # int32最小值
            MAX_SAFE_VALUE = 2147483647   # int32最大值
            MIN_LOGICAL_VALUE = 0         # 逻辑坐标最小值
            MAX_LOGICAL_VALUE = 10000     # 逻辑坐标最大值

            # 安全转换每个值
            safe_values = []
            has_overflow = False
            has_unreasonable = False

            for i, raw_val in enumerate(raw_values):
                try:
                    # 转换为float（log_ax是float32数组）
                    float_val = float(raw_val)

                    # 检查是否超出安全整数范围
                    if float_val < MIN_SAFE_VALUE or float_val > MAX_SAFE_VALUE:
                        print(f"[ctdet警告] 🚨 检测到logic_axis整数溢出: index={i}, value={float_val}, "
                              f"img_id={img_id}, ann_id={ann.get('id', 'unknown')}")
                        has_overflow = True
                        # 截断到安全范围
                        float_val = max(MIN_SAFE_VALUE, min(MAX_SAFE_VALUE, float_val))
                        print(f"[ctdet修复] 🔧 已截断到安全值: index={i}, safe_value={float_val}")

                    # 检查是否超出合理逻辑范围
                    if float_val < MIN_LOGICAL_VALUE or float_val > MAX_LOGICAL_VALUE:
                        print(f"[ctdet警告] ⚠️  检测到不合理logic_axis: index={i}, value={float_val}, "
                              f"img_id={img_id}, ann_id={ann.get('id', 'unknown')}")
                        has_unreasonable = True
                        # 截断到合理范围
                        float_val = max(MIN_LOGICAL_VALUE, min(MAX_LOGICAL_VALUE, float_val))
                        print(f"[ctdet修复] 🔧 已截断到合理值: index={i}, reasonable_value={float_val}")

                    safe_values.append(float_val)

                except (ValueError, TypeError) as e:
                    print(f"[ctdet错误] ❌ logic_axis数值转换失败: index={i}, raw_value={raw_val}, "
                          f"error={e}, img_id={img_id}")
                    # 使用默认值
                    safe_values.append(0.0)

            # 赋值到log_ax数组
            log_ax[k] = safe_values[0], safe_values[1], safe_values[2], safe_values[3]

            # 记录处理结果
            if has_overflow:
                print(f"[ctdet修复] 🚨 已处理logic_axis整数溢出: img_id={img_id}, "
                      f"原始值={raw_values}, 安全值={safe_values}")
            elif has_unreasonable:
                print(f"[ctdet修复] ⚠️  已处理不合理logic_axis: img_id={img_id}, "
                      f"原始值={raw_values}, 合理值={safe_values}")

            # 验证最终值的合理性
            if len(safe_values) == 4:
                start_row, end_row, start_col, end_col = safe_values
                if start_row > end_row or start_col > end_col:
                    print(f"[ctdet警告] ⚠️  logic_axis逻辑不一致: img_id={img_id}, "
                          f"start_row={start_row}, end_row={end_row}, start_col={start_col}, end_col={end_col}")

        except Exception as e:
            print(f"[ctdet错误] ❌ logic_axis处理发生未预期错误: {e}, img_id={img_id}, "
                  f"ann_id={ann.get('id', 'unknown')}")
            # 使用默认值
            log_ax[k] = 0.0, 0.0, 0.0, 0.0

     
        gt_det.append([ct[0] - 1. * CorNer[0], ct[1] - 1. * CorNer[1],
                       ct[0] - 1. * CorNer[2], ct[1] - 1. * CorNer[3],
                       ct[0] - 1. * CorNer[4], ct[1] - 1. * CorNer[5], 
                       ct[0] - 1. * CorNer[6], ct[1] - 1. * CorNer[7], 1, cls_id])
        
    hm_mask_v = hm_mask.reshape(1, hm_mask.shape[0])
  
    inp = (inp.astype(np.float32) / 255.)
    if self.split == 'train' and not self.opt.no_color_aug:
      color_aug(self._data_rng, inp, self._eig_val, self._eig_vec)
    

    inp = (inp - self.mean) / self.std
    inp = inp.transpose(2, 0, 1)

    # 全面数据验证（Bug修复 - 防止所有整数溢出）
    def validate_integer_arrays():
        """验证所有整数数组，防止PyTorch collate溢出"""
        try:
            MAX_SAFE_INT = 2147483647  # int32最大值
            MIN_SAFE_INT = -2147483648  # int32最小值

            # 检查所有整数数组
            integer_arrays = {
                'hm_ind': hm_ind,
                'mk_ind': mk_ind,
                'reg_ind': reg_ind,
                'ctr_cro_ind': ctr_cro_ind,
                'cc_match': cc_match,
                'h_pair_ind': h_pair_ind,
                'v_pair_ind': v_pair_ind
            }

            overflow_detected = False

            for name, arr in integer_arrays.items():
                if arr is None:
                    continue

                # 检查数值范围
                if np.any(arr > MAX_SAFE_INT) or np.any(arr < MIN_SAFE_INT):
                    overflow_detected = True
                    print(f"[ctdet警告] 🚨 检测到{name}数组溢出: "
                          f"max={arr.max()}, min={arr.min()}, img_id={img_id}")

                    # 截断到安全范围
                    arr_clipped = np.clip(arr, MIN_SAFE_INT, MAX_SAFE_INT)

                    # 更新原数组
                    if name == 'hm_ind':
                        hm_ind[:] = arr_clipped
                    elif name == 'mk_ind':
                        mk_ind[:] = arr_clipped
                    elif name == 'reg_ind':
                        reg_ind[:] = arr_clipped
                    elif name == 'ctr_cro_ind':
                        ctr_cro_ind[:] = arr_clipped
                    elif name == 'cc_match':
                        cc_match[:] = arr_clipped
                    elif name == 'h_pair_ind':
                        h_pair_ind[:] = arr_clipped
                    elif name == 'v_pair_ind':
                        v_pair_ind[:] = arr_clipped

                    print(f"[ctdet修复] 🔧 已截断{name}到安全范围: "
                          f"new_max={arr_clipped.max()}, new_min={arr_clipped.min()}")

            if overflow_detected:
                print(f"[ctdet修复] 🚨 已处理整数数组溢出问题: img_id={img_id}")

            return not overflow_detected

        except Exception as e:
            print(f"[ctdet错误] ❌ 整数数组验证失败: {e}")
            return False

    # 执行验证
    validation_passed = validate_integer_arrays()

    # PyTorch兼容性增强 - 确保logic数据类型安全
    try:
        # 验证log_ax数组的数据类型和数值范围
        if np.any(np.isnan(log_ax)) or np.any(np.isinf(log_ax)):
            print(f"[ctdet警告] ⚠️  检测到logic数组中的NaN或Inf值: img_id={img_id}")
            log_ax = np.nan_to_num(log_ax, nan=0.0, posinf=10000.0, neginf=0.0)
            print(f"[ctdet修复] 🔧 已清理NaN/Inf值")

        # 确保数据类型为float32（与PyTorch兼容）
        log_ax = log_ax.astype(np.float32)

        # 最终范围检查
        log_ax = np.clip(log_ax, 0.0, 10000.0)

        # 验证数组形状
        if log_ax.shape != (self.max_objs, 4):
            print(f"[ctdet错误] ❌ logic数组形状异常: expected=({self.max_objs}, 4), actual={log_ax.shape}")
            log_ax = np.zeros((self.max_objs, 4), dtype=np.float32)

        if hasattr(self, 'split') and self.split == 'val':
            # 验证阶段额外检查
            validation_status = "✅ 通过" if validation_passed else "⚠️  有溢出"
            # print(f"[ctdet验证] {validation_status} 数据验证: img_id={img_id}, "
            #       f"logic_range=[{log_ax.min():.2f}, {log_ax.max():.2f}]")

    except Exception as e:
        print(f"[ctdet错误] ❌ logic数组处理失败: {e}, 使用默认值")
        log_ax = np.zeros((self.max_objs, 4), dtype=np.float32)

    # 移除有问题的force_safe_dtype函数 - 原始数据类型定义已经正确

    # 移除有问题的force_safe_dtype调用 - 原始数据类型定义已经正确
    # 原始代码中索引字段已经正确定义为np.int64，无需"修复"
    # print(f"[ctdet信息] ✅ 使用原始正确的数据类型: img_id={img_id}")

    ret = {'input': inp, 'hm': hm, 'hm_ind':hm_ind, 'hm_mask':hm_mask, 'mk_ind':mk_ind, 'mk_mask':mk_mask, 'reg':reg,'reg_ind':reg_ind,'reg_mask': reg_mask, \
           'wh': wh,'st':st, 'ctr_cro_ind':ctr_cro_ind, 'cc_match': cc_match, 'hm_ctxy':hm_ctxy, 'logic': log_ax, 'h_pair_ind': h_pair_ind, 'v_pair_ind': v_pair_ind}

    if self.opt.dense_wh:
      hm_a = hm.max(axis=0, keepdims=True)
      dense_wh_mask = np.concatenate([hm_a, hm_a], axis=0)
      ret.update({'dense_wh': dense_wh, 'dense_wh_mask': dense_wh_mask})
      del ret['wh']
    elif self.opt.cat_spec_wh:
      ret.update({'cat_spec_wh': cat_spec_wh, 'cat_spec_mask': cat_spec_mask})
      del ret['wh']
    if self.opt.reg_offset:
      ret.update({'reg': reg})
    if self.opt.debug > 0 or not self.split == 'train':
      gt_det = np.array(gt_det, dtype=np.float32) if len(gt_det) > 0 else \
               np.zeros((1, 10), dtype=np.float32)
      meta = {'c': c, 's': s, 'rot':rot, 'gt_det': gt_det, 'img_id': img_id}
      ret['meta'] = meta
    return ret
