"""
TEDS (Tree Edit Distance based Similarity) Metric for Table Structure Recognition

基于LORE项目数据格式的TEDS评价指标实现
支持从COCO格式标注（包含logic_axis）生成HTML树结构并计算TEDS分数

Author: LORE-TSR-adapt Project
Date: 2025-01-24
"""

import json
import re
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict
import numpy as np
from lxml import etree, html
from apted import APTED
from apted.helpers import Tree


class TEDSMetric:
    """
    TEDS评价指标计算器
    
    支持从LORE项目的COCO格式标注数据计算TEDS分数
    """
    
    def __init__(self, structure_only: bool = False):
        """
        初始化TEDS计算器
        
        Args:
            structure_only (bool): 是否只计算结构TEDS（忽略内容差异）
        """
        self.structure_only = structure_only
        
    def compute_teds(self, pred_annotations: List[Dict], gt_annotations: List[Dict], 
                     pred_content: Optional[Dict] = None, gt_content: Optional[Dict] = None) -> float:
        """
        计算TEDS分数
        
        Args:
            pred_annotations: 预测的标注列表（COCO格式，包含logic_axis）
            gt_annotations: 真实的标注列表（COCO格式，包含logic_axis）
            pred_content: 预测的单元格内容（可选）
            gt_content: 真实的单元格内容（可选）
            
        Returns:
            float: TEDS分数 (0-1之间)
        """
        try:
            # 1. 从标注数据构建HTML树
            pred_html = self._build_html_from_annotations(pred_annotations, pred_content)
            gt_html = self._build_html_from_annotations(gt_annotations, gt_content)
            
            # 2. 解析HTML为树结构
            pred_tree = self._parse_html_to_tree(pred_html)
            gt_tree = self._parse_html_to_tree(gt_html)
            
            # 3. 计算树编辑距离
            ted = self._compute_tree_edit_distance(pred_tree, gt_tree)
            
            # 4. 计算TEDS分数
            max_nodes = max(self._count_nodes(pred_tree), self._count_nodes(gt_tree))
            if max_nodes == 0:
                return 1.0
            
            teds_score = 1.0 - (ted / max_nodes)
            return max(0.0, teds_score)
            
        except Exception as e:
            print(f"TEDS计算错误: {e}")
            return 0.0
    
    def _build_html_from_annotations(self, annotations: List[Dict], 
                                   content: Optional[Dict] = None) -> str:
        """
        从COCO格式标注构建HTML表格
        
        Args:
            annotations: COCO格式标注列表
            content: 单元格内容字典（可选）
            
        Returns:
            str: HTML表格字符串
        """
        if not annotations:
            return "<table></table>"
        
        # 1. 解析logic_axis信息，构建表格网格
        grid_cells = {}
        max_row, max_col = 0, 0
        
        for ann in annotations:
            logic_axis = ann.get('logic_axis', [[0, 0, 0, 0]])[0]
            row_start, col_start, row_end, col_end = logic_axis
            
            # 处理跨行跨列的情况
            if row_end == 0:
                row_end = row_start
            if col_end == 0:
                col_end = col_start
                
            max_row = max(max_row, row_end)
            max_col = max(max_col, col_end)
            
            # 获取单元格内容
            cell_content = ""
            if content and str(ann.get('id', '')) in content:
                cell_content = content[str(ann['id'])]
            elif not self.structure_only:
                cell_content = f"cell_{ann.get('id', 'unknown')}"
            
            # 计算跨行跨列
            rowspan = row_end - row_start + 1 if row_end > row_start else 1
            colspan = col_end - col_start + 1 if col_end > col_start else 1
            
            grid_cells[(row_start, col_start)] = {
                'content': cell_content,
                'rowspan': rowspan,
                'colspan': colspan,
                'id': ann.get('id', 'unknown')
            }
        
        # 2. 构建HTML表格
        html_parts = ['<table>']
        
        for row in range(max_row + 1):
            html_parts.append('<tr>')
            
            for col in range(max_col + 1):
                if (row, col) in grid_cells:
                    cell = grid_cells[(row, col)]
                    
                    # 构建td标签
                    td_attrs = []
                    if cell['rowspan'] > 1:
                        td_attrs.append(f'rowspan="{cell["rowspan"]}"')
                    if cell['colspan'] > 1:
                        td_attrs.append(f'colspan="{cell["colspan"]}"')
                    
                    attrs_str = ' ' + ' '.join(td_attrs) if td_attrs else ''
                    html_parts.append(f'<td{attrs_str}>{cell["content"]}</td>')
                else:
                    # 检查是否被跨行跨列单元格占用
                    occupied = False
                    for (r, c), cell in grid_cells.items():
                        if (r <= row < r + cell['rowspan'] and 
                            c <= col < c + cell['colspan'] and 
                            (r, c) != (row, col)):
                            occupied = True
                            break
                    
                    if not occupied:
                        html_parts.append('<td></td>')
            
            html_parts.append('</tr>')
        
        html_parts.append('</table>')
        return ''.join(html_parts)
    
    def _parse_html_to_tree(self, html_str: str) -> Tree:
        """
        将HTML字符串解析为APTED树结构
        
        Args:
            html_str: HTML字符串
            
        Returns:
            Tree: APTED树对象
        """
        try:
            # 解析HTML
            doc = html.fromstring(html_str)
            
            # 转换为APTED树
            return self._element_to_tree(doc)
            
        except Exception as e:
            print(f"HTML解析错误: {e}")
            return Tree('table')
    
    def _element_to_tree(self, element) -> Tree:
        """
        递归将HTML元素转换为APTED树
        
        Args:
            element: HTML元素
            
        Returns:
            Tree: APTED树节点
        """
        # 获取标签名
        tag_name = element.tag.lower()
        
        # 获取属性（用于结构比较）
        attrs = []
        if not self.structure_only:
            for key, value in element.attrib.items():
                if key in ['rowspan', 'colspan']:
                    attrs.append(f'{key}={value}')
        
        # 构建节点标签
        node_label = tag_name
        if attrs:
            node_label += '[' + ','.join(attrs) + ']'
        
        # 获取文本内容
        text_content = (element.text or '').strip()
        if text_content and not self.structure_only:
            node_label += f':{text_content}'
        
        # 创建子树
        children = []
        for child in element:
            children.append(self._element_to_tree(child))
        
        # 处理尾部文本
        if element.tail and element.tail.strip() and not self.structure_only:
            children.append(Tree(f'text:{element.tail.strip()}'))
        
        return Tree(node_label, children)
    
    def _compute_tree_edit_distance(self, tree1: Tree, tree2: Tree) -> float:
        """
        计算两棵树之间的编辑距离
        
        Args:
            tree1: 第一棵树
            tree2: 第二棵树
            
        Returns:
            float: 树编辑距离
        """
        try:
            apted = APTED(tree1, tree2)
            return apted.compute_edit_distance()
        except Exception as e:
            print(f"树编辑距离计算错误: {e}")
            return float('inf')
    
    def _count_nodes(self, tree: Tree) -> int:
        """
        计算树中节点数量
        
        Args:
            tree: 树对象
            
        Returns:
            int: 节点数量
        """
        count = 1  # 当前节点
        for child in tree.children:
            count += self._count_nodes(child)
        return count


class LORETableStructureConverter:
    """
    LORE表格结构转换器
    
    用于将LORE项目的预测结果转换为TEDS评价所需的格式
    """
    
    @staticmethod
    def convert_predictions_to_annotations(predictions: Dict, image_id: int, 
                                         confidence_threshold: float = 0.5) -> List[Dict]:
        """
        将LORE模型预测结果转换为COCO格式标注
        
        Args:
            predictions: LORE模型预测结果
            image_id: 图像ID
            confidence_threshold: 置信度阈值
            
        Returns:
            List[Dict]: COCO格式标注列表
        """
        annotations = []
        
        # 从预测结果中提取信息
        if 'results' in predictions:
            results = predictions['results']
            
            for i, result in enumerate(results):
                if result.get('score', 0) < confidence_threshold:
                    continue
                
                # 构建标注
                annotation = {
                    'id': f"pred_{image_id}_{i}",
                    'image_id': image_id,
                    'category_id': 1,
                    'bbox': result.get('bbox', [0, 0, 0, 0]),
                    'segmentation': [result.get('segmentation', [0, 0, 0, 0, 0, 0, 0, 0])],
                    'logic_axis': [result.get('logic_axis', [0, 0, 0, 0])],
                    'area': result.get('area', 0),
                    'iscrowd': 0,
                    'ignore': 0
                }
                
                annotations.append(annotation)
        
        return annotations


def evaluate_teds_on_dataset(pred_results: Dict, gt_annotations: Dict, 
                           structure_only: bool = False) -> Dict[str, float]:
    """
    在数据集上评估TEDS指标
    
    Args:
        pred_results: 预测结果字典 {image_id: predictions}
        gt_annotations: 真实标注字典 {image_id: annotations}
        structure_only: 是否只评估结构
        
    Returns:
        Dict[str, float]: 评估结果
    """
    teds_calculator = TEDSMetric(structure_only=structure_only)
    converter = LORETableStructureConverter()
    
    teds_scores = []
    
    for image_id in gt_annotations:
        if image_id not in pred_results:
            teds_scores.append(0.0)
            continue
        
        # 获取真实标注
        gt_anns = gt_annotations[image_id]
        
        # 转换预测结果
        pred_anns = converter.convert_predictions_to_annotations(
            pred_results[image_id], image_id
        )
        
        # 计算TEDS
        teds_score = teds_calculator.compute_teds(pred_anns, gt_anns)
        teds_scores.append(teds_score)
    
    # 计算统计信息
    results = {
        'teds_mean': np.mean(teds_scores),
        'teds_std': np.std(teds_scores),
        'teds_min': np.min(teds_scores),
        'teds_max': np.max(teds_scores),
        'num_samples': len(teds_scores)
    }
    
    return results
