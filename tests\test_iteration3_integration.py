#!/usr/bin/env python3
"""
迭代三专用集成测试 - 步骤3.5
综合测试迭代三所有组件的协同工作，包括：
- 步骤3.1：日志配置模块
- 步骤3.2：质量筛选核心模块  
- 步骤3.3：异常处理和报告机制
- 步骤3.4：数据集系统集成
"""

import sys
import os
import json
import tempfile
import time
import unittest
from typing import Dict, List, Any

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestIteration3Integration(unittest.TestCase):
    """迭代三集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_data_created = False
        
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def create_comprehensive_test_dataset(self) -> str:
        """创建综合测试数据集"""
        # 创建目录结构
        images_dir = os.path.join(self.temp_dir, "images")
        annotations_dir = os.path.join(self.temp_dir, "annotations")
        os.makedirs(images_dir, exist_ok=True)
        os.makedirs(annotations_dir, exist_ok=True)
        
        # 测试数据模板
        base_template = {
            "table_ind": 0,
            "type": 1,
            "cells": [
                {
                    "cell_ind": 0,
                    "header": True,
                    "content": [{"bbox": None, "text": "测试内容", "score": None}],
                    "bbox": {"p1": [100, 100], "p2": [200, 100], "p3": [200, 150], "p4": [100, 150]},
                    "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}
                }
            ]
        }
        
        test_cases = [
            # 1. 合格样本（中文）
            ("qualified_cn", "合格"),
            # 2. 合格样本（英文）
            ("qualified_en", "qualified"),
            # 3. 合格样本（good）
            ("qualified_good", "good"),
            # 4. 不合格样本
            ("unqualified", "不合格"),
            # 5. 缺失quality字段
            ("missing_quality", None),
            # 6. 质量字段类型错误
            ("wrong_type", 123),
            # 7. 大小写测试
            ("case_test", "QUALIFIED"),
        ]
        
        for i, (name, quality) in enumerate(test_cases):
            # 创建图像文件
            image_path = os.path.join(images_dir, f"{name}.jpg")
            with open(image_path, 'w') as f:
                f.write(f"fake image data {i}")
            
            # 创建标注文件
            annotation_path = os.path.join(annotations_dir, f"{name}.json")
            data = base_template.copy()
            data["table_ind"] = i
            
            if quality is not None:
                data["quality"] = quality
            # 如果quality为None，则不添加quality字段
            
            with open(annotation_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 8. 创建JSON格式错误的文件
        image_error = os.path.join(images_dir, "json_error.jpg")
        annotation_error = os.path.join(annotations_dir, "json_error.json")
        
        with open(image_error, 'w') as f:
            f.write("fake image data")
        
        with open(annotation_error, 'w') as f:
            f.write('{"quality": "合格", "table_ind": 0, "cells": [')  # 未闭合的JSON
        
        # 9. 创建孤儿标注文件（没有对应图像）
        orphan_annotation = os.path.join(annotations_dir, "orphan.json")
        orphan_data = base_template.copy()
        orphan_data["quality"] = "合格"
        with open(orphan_annotation, 'w', encoding='utf-8') as f:
            json.dump(orphan_data, f, ensure_ascii=False, indent=2)
        
        self.test_data_created = True
        return self.temp_dir
    
    def test_step_3_1_logger_config(self):
        """测试步骤3.1：日志配置模块"""
        from lib.utils.logger_config import LoggerConfig
        
        # 测试默认配置
        default_config = LoggerConfig.get_default_config()
        self.assertIsInstance(default_config, dict)
        self.assertIn("level", default_config)
        self.assertIn("format", default_config)
        
        # 测试日志记录器创建
        logger = LoggerConfig.setup_logger("test_integration")
        self.assertIsNotNone(logger)
        
        # 测试日志记录
        logger.info("集成测试 - 日志配置模块验证")
        logger.warning("测试警告信息")
        logger.error("测试错误信息")
        
        print("✅ 步骤3.1：日志配置模块测试通过")
    
    def test_step_3_2_quality_filter_core(self):
        """测试步骤3.2：质量筛选核心模块"""
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        # 创建测试数据
        test_dir = self.create_comprehensive_test_dataset()
        
        # 模拟文件索引
        file_index = {}
        images_dir = os.path.join(test_dir, "images")
        annotations_dir = os.path.join(test_dir, "annotations")
        
        for i, filename in enumerate(os.listdir(images_dir)):
            if filename.endswith('.jpg'):
                name = filename[:-4]
                file_index[i] = {
                    "image_path": os.path.join(images_dir, filename),
                    "annotation_path": os.path.join(annotations_dir, f"{name}.json")
                }
        
        # 创建质量筛选器
        logger = LoggerConfig.setup_logger("test_quality_filter")
        quality_filter = QualityFilter(logger=logger)
        
        # 执行质量筛选
        result = quality_filter.filter_samples(file_index, "test")
        
        # 验证结果
        self.assertIn("filtered_index", result)
        self.assertIn("statistics", result)
        self.assertIn("exception_report", result)
        
        stats = result["statistics"]
        self.assertGreater(stats["total_processed"], 0)
        self.assertGreaterEqual(stats["valid_samples"], 0)
        self.assertGreaterEqual(stats["filtered_samples"], 0)
        
        # 验证应该有4个合格样本（"合格", "qualified", "good", "QUALIFIED"）
        # 注意：QUALIFIED在大小写不敏感模式下也会被接受
        expected_valid = 4
        self.assertEqual(stats["valid_samples"], expected_valid)
        
        print(f"✅ 步骤3.2：质量筛选核心模块测试通过 - 有效样本: {stats['valid_samples']}")
    
    def test_step_3_3_exception_handling(self):
        """测试步骤3.3：异常处理和报告机制"""
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        # 创建测试数据
        test_dir = self.create_comprehensive_test_dataset()
        
        # 创建包含各种异常的文件索引
        file_index = {}
        images_dir = os.path.join(test_dir, "images")
        annotations_dir = os.path.join(test_dir, "annotations")
        
        # 添加正常文件
        file_index[1] = {
            "image_path": os.path.join(images_dir, "qualified_cn.jpg"),
            "annotation_path": os.path.join(annotations_dir, "qualified_cn.json")
        }
        
        # 添加JSON错误文件
        file_index[2] = {
            "image_path": os.path.join(images_dir, "json_error.jpg"),
            "annotation_path": os.path.join(annotations_dir, "json_error.json")
        }
        
        # 添加孤儿标注文件
        file_index[3] = {
            "image_path": os.path.join(images_dir, "orphan.jpg"),  # 不存在的图像
            "annotation_path": os.path.join(annotations_dir, "orphan.json")
        }
        
        # 添加缺失字段文件
        file_index[4] = {
            "image_path": os.path.join(images_dir, "missing_quality.jpg"),
            "annotation_path": os.path.join(annotations_dir, "missing_quality.json")
        }
        
        # 添加类型错误文件
        file_index[5] = {
            "image_path": os.path.join(images_dir, "wrong_type.jpg"),
            "annotation_path": os.path.join(annotations_dir, "wrong_type.json")
        }
        
        # 创建质量筛选器
        logger = LoggerConfig.setup_logger("test_exception_handling")
        quality_filter = QualityFilter(logger=logger)
        
        # 执行质量筛选
        result = quality_filter.filter_samples(file_index, "test")
        
        # 验证异常报告
        report = result["exception_report"]

        # 验证各种异常都被正确检测
        self.assertGreater(len(report["file_missing"]["orphan_images"]), 0)  # 孤儿图像
        # JSON错误可能被归类为文件访问错误或其他类型，检查总的异常数量
        total_errors = (len(report["format_errors"]["json_syntax_errors"]) +
                       len(report["file_access_errors"]) +
                       len(report["format_errors"]["missing_fields"]) +
                       len(report["format_errors"]["type_errors"]))
        self.assertGreater(total_errors, 2)  # 至少应该有缺失字段、类型错误等
        self.assertGreater(len(report["format_errors"]["missing_fields"]), 0)  # 缺失字段
        self.assertGreater(len(report["format_errors"]["type_errors"]), 0)  # 类型错误
        
        print("✅ 步骤3.3：异常处理和报告机制测试通过")
    
    def test_step_3_4_dataset_integration(self):
        """测试步骤3.4：数据集系统集成"""
        from lib.datasets.parsers import QualityFilter, FileScanner, TableLabelMeParser
        from lib.utils.logger_config import LoggerConfig
        
        # 验证模块导入
        self.assertIsNotNone(QualityFilter)
        self.assertIsNotNone(FileScanner)
        self.assertIsNotNone(TableLabelMeParser)
        self.assertIsNotNone(LoggerConfig)
        
        # 测试质量筛选器独立功能
        logger = LoggerConfig.setup_logger("test_dataset_integration")
        quality_filter = QualityFilter(logger=logger)
        
        # 验证配置
        config = quality_filter.config
        self.assertIn("accepted_values", config)
        self.assertIn("case_sensitive", config)
        self.assertIn("quality_field_path", config)
        
        print("✅ 步骤3.4：数据集系统集成测试通过")
    
    def test_end_to_end_workflow(self):
        """端到端工作流程测试"""
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers import QualityFilter, FileScanner
        
        print("=== 端到端工作流程测试 ===")
        
        # 创建测试数据
        test_dir = self.create_comprehensive_test_dataset()
        
        # 1. 创建日志记录器
        logger = LoggerConfig.setup_logger("end_to_end_test")
        logger.info("开始端到端工作流程测试")
        
        # 2. 创建文件扫描器
        file_scanner = FileScanner()
        
        # 3. 创建质量筛选器
        quality_filter = QualityFilter(logger=logger)
        
        # 4. 模拟文件扫描结果
        images_dir = os.path.join(test_dir, "images")
        annotations_dir = os.path.join(test_dir, "annotations")
        
        file_index = {}
        for i, filename in enumerate(os.listdir(images_dir)):
            if filename.endswith('.jpg'):
                name = filename[:-4]
                annotation_file = f"{name}.json"
                if os.path.exists(os.path.join(annotations_dir, annotation_file)):
                    file_index[i] = {
                        "image_path": os.path.join(images_dir, filename),
                        "annotation_path": os.path.join(annotations_dir, annotation_file)
                    }
        
        logger.info(f"文件扫描完成，发现 {len(file_index)} 个文件对")
        
        # 5. 执行质量筛选
        start_time = time.time()
        result = quality_filter.filter_samples(file_index, "test")
        end_time = time.time()
        
        # 6. 验证结果
        stats = result["statistics"]
        report = result["exception_report"]
        
        logger.info(f"质量筛选完成，耗时 {end_time - start_time:.3f} 秒")
        logger.info(f"处理结果：总计 {stats['total_processed']}，"
                   f"有效 {stats['valid_samples']}，"
                   f"筛选 {stats['filtered_samples']}，"
                   f"错误 {stats['error_samples']}")
        
        # 验证预期结果
        self.assertGreater(stats["total_processed"], 0)
        self.assertEqual(stats["valid_samples"], 4)  # 应该有4个合格样本（包括QUALIFIED）
        self.assertGreater(stats["filtered_samples"], 0)  # 应该有被筛选的样本
        
        # 验证成功率
        success_rate = report["summary"]["success_rate"]
        self.assertGreater(success_rate, 0)
        self.assertLessEqual(success_rate, 100)
        
        print(f"✅ 端到端工作流程测试通过 - 成功率: {success_rate:.1f}%")
    
    def test_performance_and_stability(self):
        """性能和稳定性测试"""
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        print("=== 性能和稳定性测试 ===")
        
        # 创建大量测试数据
        large_test_dir = tempfile.mkdtemp()
        try:
            images_dir = os.path.join(large_test_dir, "images")
            annotations_dir = os.path.join(large_test_dir, "annotations")
            os.makedirs(images_dir, exist_ok=True)
            os.makedirs(annotations_dir, exist_ok=True)
            
            # 创建100个测试文件
            file_index = {}
            for i in range(100):
                image_path = os.path.join(images_dir, f"test_{i}.jpg")
                annotation_path = os.path.join(annotations_dir, f"test_{i}.json")
                
                with open(image_path, 'w') as f:
                    f.write(f"fake image {i}")
                
                quality = "合格" if i % 3 == 0 else "不合格"
                data = {
                    "table_ind": i,
                    "quality": quality,
                    "cells": []
                }
                
                with open(annotation_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False)
                
                file_index[i] = {
                    "image_path": image_path,
                    "annotation_path": annotation_path
                }
            
            # 执行性能测试
            logger = LoggerConfig.setup_logger("performance_test")
            quality_filter = QualityFilter(logger=logger)
            
            start_time = time.time()
            result = quality_filter.filter_samples(file_index, "performance_test")
            end_time = time.time()
            
            processing_time = end_time - start_time
            files_per_second = len(file_index) / processing_time
            
            # 验证性能
            self.assertLess(processing_time, 10.0)  # 应该在10秒内完成
            self.assertGreater(files_per_second, 5)  # 每秒至少处理5个文件
            
            # 验证结果正确性
            stats = result["statistics"]
            expected_valid = len([i for i in range(100) if i % 3 == 0])  # 合格的文件数
            self.assertEqual(stats["valid_samples"], expected_valid)
            
            print(f"✅ 性能测试通过 - 处理时间: {processing_time:.3f}s, "
                  f"速度: {files_per_second:.1f} files/s")
            
        finally:
            import shutil
            shutil.rmtree(large_test_dir)
    
    def test_configuration_flexibility(self):
        """配置灵活性测试"""
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        print("=== 配置灵活性测试 ===")
        
        # 创建测试数据
        test_dir = self.create_comprehensive_test_dataset()
        
        # 创建测试文件索引
        file_index = {
            1: {
                "image_path": os.path.join(test_dir, "images", "case_test.jpg"),
                "annotation_path": os.path.join(test_dir, "annotations", "case_test.json")
            }
        }
        
        logger = LoggerConfig.setup_logger("config_test")
        
        # 测试1：大小写不敏感（默认）
        config1 = {"case_sensitive": False}
        filter1 = QualityFilter(config=config1, logger=logger)
        result1 = filter1.filter_samples(file_index, "test")
        
        # 测试2：大小写敏感
        config2 = {"case_sensitive": True, "accepted_values": ["qualified"]}
        filter2 = QualityFilter(config=config2, logger=logger)
        result2 = filter2.filter_samples(file_index, "test")
        
        # 测试3：严格模式
        config3 = {"strict_mode": True}
        filter3 = QualityFilter(config=config3, logger=logger)
        
        # 验证配置效果
        self.assertEqual(result1["statistics"]["valid_samples"], 1)  # 大小写不敏感应该通过
        self.assertEqual(result2["statistics"]["valid_samples"], 0)  # 大小写敏感应该被筛选
        
        print("✅ 配置灵活性测试通过")


def run_integration_tests():
    """运行集成测试"""
    print("迭代三专用集成测试 - 步骤3.5")
    print("=" * 60)
    print()
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestIteration3Integration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("集成测试结果汇总")
    print("=" * 60)
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\n成功率: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        print("\n🎉 所有集成测试通过！迭代三功能验证成功！")
        return 0
    else:
        print("\n❌ 部分集成测试失败，请检查详细日志")
        return 1


if __name__ == "__main__":
    exit(run_integration_tests())
