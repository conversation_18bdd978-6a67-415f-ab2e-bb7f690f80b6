"""
TableLabelMe格式的具体解析器实现，负责格式转换和数据标准化。

该解析器将TableLabelMe格式的标注数据转换为LORE-TSR兼容的标准格式，
包括坐标转换、逻辑结构转换、面积计算等核心功能。
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

import math
from typing import Dict, Any, List, Optional, Union

from .base_parser import BaseParser


class TableLabelMeParser(BaseParser):
    """
    TableLabelMe格式解析器，继承BaseParser基类。
    
    负责将TableLabelMe格式的标注数据转换为LORE-TSR标准格式，
    确保与现有训练流程完全兼容。
    """
    
    def parse_file(self, json_path: str, image_path: str) -> Optional[List[Dict[str, Any]]]:
        """
        解析TableLabelMe格式文件，转换为LORE-TSR标准格式。
        
        Args:
            json_path (str): TableLabelMe JSON文件路径
            image_path (str): 对应图像文件路径
            
        Returns:
            Optional[List[Dict[str, Any]]]: 包含所有标注的标准化字典列表，
                                          解析失败时返回None
        """
        # 加载JSON文件
        json_data = self._load_json_file(json_path)
        if json_data is None:
            return None
            
        # 生成图像ID
        image_id = self.generate_image_id(image_path)
        
        # 解析标注数据
        annotations = []
        
        # 适配真实的TableLabelMe数据格式
        if isinstance(json_data, dict) and 'cells' in json_data:
            # 真实数据格式：包含cells字段的字典
            raw_annotations = json_data['cells']
            # 保存表格级别的信息
            table_info = {
                'table_ind': json_data.get('table_ind', 0),
                'image_path': json_data.get('image_path', image_path),
                'type': json_data.get('type', 1),
                'quality': json_data.get('quality', '合格')
            }
        elif isinstance(json_data, list):
            # 如果是标注列表（兼容旧格式）
            raw_annotations = json_data
            table_info = {}
        elif isinstance(json_data, dict) and 'annotations' in json_data:
            # 如果是包含annotations字段的字典（兼容旧格式）
            raw_annotations = json_data['annotations']
            table_info = {}
        elif isinstance(json_data, dict):
            # 如果是单个标注对象（兼容旧格式）
            raw_annotations = [json_data]
            table_info = {}
        else:
            return None
            
        # 质量筛选（适配真实数据格式）
        if 'cells' in json_data:
            # 真实数据格式：质量信息在表格级别
            table_quality = table_info.get('quality', '合格')
            if table_quality == '合格':
                filtered_annotations = raw_annotations
            else:
                filtered_annotations = []
        else:
            # 兼容旧格式：质量信息在每个标注中
            filtered_annotations = self.filter_by_quality(raw_annotations)
        
        # 转换每个标注
        for ann in filtered_annotations:
            try:
                # 检查必需字段（适配真实数据格式）
                if not all(key in ann for key in ['bbox', 'lloc', 'cell_ind']):
                    continue
                    
                # 转换坐标
                segmentation = self.convert_bbox_to_segmentation(ann['bbox'])
                if segmentation is None:
                    continue
                    
                # 转换逻辑轴
                logic_axis = self.convert_lloc_to_logic_axis(ann['lloc'])
                if logic_axis is None:
                    continue
                    
                # 计算面积
                area = self.calculate_area(segmentation)
                
                # 提取bbox
                bbox = self.extract_bbox_from_segmentation(segmentation)
                
                # 生成标注ID
                annotation_id = self.generate_annotation_id(image_id, ann['cell_ind'])
                
                # 构建标准化数据结构（适配真实数据格式）
                standard_annotation = {
                    'image_id': image_id,
                    'annotation_id': annotation_id,
                    'category_id': 1,  # 固定为1（单元格类别）
                    'segmentation': segmentation,
                    'logic_axis': logic_axis,
                    'area': area,
                    'bbox': bbox,
                    'extra_info': {
                        'table_ind': table_info.get('table_ind', ann.get('table_ind', 0)),
                        'type': ann.get('type', 'cell'),
                        'header': ann.get('header', False),  # 真实数据中的header字段
                        'border': ann.get('border', {}),     # 真实数据中的border字段
                        'content': ann.get('content', []),   # 真实数据中的content字段
                        'quality': table_info.get('quality', ann.get('quality', '合格'))
                    }
                }
                
                # 验证数据
                if self.validate_data(standard_annotation):
                    annotations.append(standard_annotation)
                    
            except (KeyError, TypeError, ValueError):
                # 遵循fail-fast原则，单个标注错误不影响其他标注
                continue
                
        return annotations if annotations else None

    def convert_bbox_to_segmentation(self, bbox: Dict[str, Union[List[float], Dict[str, Union[int, float]]]]) -> Optional[List[float]]:
        """
        将四个角点坐标转换为一维segmentation数组（适配真实数据格式）。

        Args:
            bbox (Dict): 包含p1-p4四个点的字典，支持两种格式：
                        1. 真实格式：{"p1": [x, y], "p2": [x, y], ...}
                        2. 旧格式：{"p1": {"x": float, "y": float}, ...}

        Returns:
            Optional[List[float]]: [p1.x, p1.y, p2.x, p2.y, p3.x, p3.y, p4.x, p4.y]
                                  转换失败时返回None
        """
        try:
            # 提取四个角点坐标
            p1 = bbox['p1']
            p2 = bbox['p2']
            p3 = bbox['p3']
            p4 = bbox['p4']

            # 适配两种数据格式
            if isinstance(p1, list):
                # 真实数据格式：[x, y]
                segmentation = [
                    float(p1[0]), float(p1[1]),
                    float(p2[0]), float(p2[1]),
                    float(p3[0]), float(p3[1]),
                    float(p4[0]), float(p4[1])
                ]
            else:
                # 旧数据格式：{"x": float, "y": float}
                segmentation = [
                    float(p1['x']), float(p1['y']),
                    float(p2['x']), float(p2['y']),
                    float(p3['x']), float(p3['y']),
                    float(p4['x']), float(p4['y'])
                ]

            return segmentation

        except (KeyError, TypeError, ValueError, IndexError):
            return None

    def convert_lloc_to_logic_axis(self, lloc: Dict[str, Union[int, float]]) -> Optional[List[int]]:
        """
        将逻辑位置信息转换为logic_axis格式（Bug修复版本）。

        实现安全的数值范围限制和类型转换，防止PyTorch整数溢出错误。

        Args:
            lloc (Dict): 包含行列位置信息的字典

        Returns:
            Optional[List[int]]: [start_row, end_row, start_col, end_col]
                               转换失败时返回None
        """
        try:
            # 定义安全的数值范围（PyTorch int32兼容）
            MIN_SAFE_VALUE = -2147483648  # int32最小值
            MAX_SAFE_VALUE = 2147483647   # int32最大值

            # 定义合理的表格逻辑坐标范围
            MIN_LOGICAL_VALUE = 0         # 逻辑坐标不应为负数
            MAX_LOGICAL_VALUE = 10000     # 合理的最大表格行列数

            # 提取原始值
            raw_values = {
                'start_row': lloc.get('start_row'),
                'end_row': lloc.get('end_row'),
                'start_col': lloc.get('start_col'),
                'end_col': lloc.get('end_col')
            }

            # 检查必需字段是否存在
            missing_fields = [k for k, v in raw_values.items() if v is None]
            if missing_fields:
                print(f"[Parser警告] lloc缺少必需字段: {missing_fields}")
                return None

            # 安全转换和范围检查
            converted_values = []
            has_overflow = False
            has_unreasonable = False

            for field_name, raw_value in raw_values.items():
                try:
                    # 尝试转换为整数
                    int_value = int(float(raw_value))  # 先转float再转int，处理字符串数字

                    # 检查是否超出安全范围
                    if int_value < MIN_SAFE_VALUE or int_value > MAX_SAFE_VALUE:
                        print(f"[Parser错误] 🚨 检测到整数溢出风险: {field_name}={int_value}")
                        has_overflow = True
                        # 截断到安全范围
                        int_value = max(MIN_SAFE_VALUE, min(MAX_SAFE_VALUE, int_value))
                        print(f"[Parser修复] 🔧 已截断到安全值: {field_name}={int_value}")

                    # 检查是否超出合理范围
                    if int_value < MIN_LOGICAL_VALUE or int_value > MAX_LOGICAL_VALUE:
                        print(f"[Parser警告] ⚠️  检测到不合理的逻辑坐标: {field_name}={int_value}")
                        has_unreasonable = True
                        # 截断到合理范围
                        int_value = max(MIN_LOGICAL_VALUE, min(MAX_LOGICAL_VALUE, int_value))
                        print(f"[Parser修复] 🔧 已截断到合理值: {field_name}={int_value}")

                    converted_values.append(int_value)

                except (ValueError, TypeError) as e:
                    print(f"[Parser错误] ❌ 数值转换失败: {field_name}={raw_value}, 错误: {e}")
                    return None

            # 构建logic_axis数组
            logic_axis = converted_values

            # 逻辑一致性检查
            start_row, end_row, start_col, end_col = logic_axis
            if start_row > end_row:
                print(f"[Parser警告] ⚠️  逻辑坐标不一致: start_row({start_row}) > end_row({end_row})")
            if start_col > end_col:
                print(f"[Parser警告] ⚠️  逻辑坐标不一致: start_col({start_col}) > end_col({end_col})")

            # 记录处理结果
            if has_overflow:
                print(f"[Parser修复] 🚨 已处理整数溢出问题: 原始值={raw_values}, 安全值={logic_axis}")
            elif has_unreasonable:
                print(f"[Parser修复] ⚠️  已处理不合理数值: 原始值={raw_values}, 合理值={logic_axis}")

            return logic_axis

        except Exception as e:
            print(f"[Parser错误] ❌ lloc转换发生未预期错误: {e}, 输入数据: {lloc}")
            return None

    def calculate_area(self, segmentation: List[float]) -> float:
        """
        根据segmentation坐标计算单元格面积。

        Args:
            segmentation (List[float]): 8个坐标值的数组

        Returns:
            float: 计算得出的面积

        Note:
            使用鞋带公式计算多边形面积
        """
        if len(segmentation) != 8:
            return 0.0

        try:
            # 提取坐标点
            x = [segmentation[i] for i in range(0, 8, 2)]
            y = [segmentation[i] for i in range(1, 8, 2)]

            # 使用鞋带公式计算面积
            area = 0.0
            n = len(x)
            for i in range(n):
                j = (i + 1) % n
                area += x[i] * y[j]
                area -= x[j] * y[i]

            return abs(area) / 2.0

        except (TypeError, ValueError, IndexError):
            return 0.0

    def extract_bbox_from_segmentation(self, segmentation: List[float]) -> List[float]:
        """
        从segmentation坐标提取bbox格式。

        Args:
            segmentation (List[float]): 8个坐标值的数组

        Returns:
            List[float]: [x, y, width, height]格式的bbox
        """
        if len(segmentation) != 8:
            return [0.0, 0.0, 0.0, 0.0]

        try:
            # 提取所有x和y坐标
            x_coords = [segmentation[i] for i in range(0, 8, 2)]
            y_coords = [segmentation[i] for i in range(1, 8, 2)]

            # 计算边界框
            min_x = min(x_coords)
            max_x = max(x_coords)
            min_y = min(y_coords)
            max_y = max(y_coords)

            # 返回[x, y, width, height]格式
            return [min_x, min_y, max_x - min_x, max_y - min_y]

        except (TypeError, ValueError, IndexError):
            return [0.0, 0.0, 0.0, 0.0]

    def filter_by_quality(self, annotations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        根据quality字段筛选合格的标注数据。

        Args:
            annotations (List[Dict]): 原始标注列表

        Returns:
            List[Dict]: 筛选后的标注列表
        """
        if not isinstance(annotations, list):
            return []

        filtered = []
        for ann in annotations:
            if isinstance(ann, dict) and ann.get('quality') == '合格':
                filtered.append(ann)

        return filtered
