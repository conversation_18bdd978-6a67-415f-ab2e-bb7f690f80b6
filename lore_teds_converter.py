#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LORE项目TEDS评价指标实现方案
TableLabelMe格式到HTML转换器

作者：AI Assistant
日期：2025-01-24
"""

import json
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from bs4 import BeautifulSoup
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CellInfo:
    """单元格信息数据类"""
    cell_ind: int
    start_row: int
    end_row: int
    start_col: int
    end_col: int
    content: str
    is_header: bool
    bbox: Dict[str, List[float]]
    
    @property
    def rowspan(self) -> int:
        return self.end_row - self.start_row + 1
    
    @property
    def colspan(self) -> int:
        return self.end_col - self.start_col + 1

class TableLabelMeToHTMLConverter:
    """TableLabelMe格式到HTML表格转换器"""
    
    def __init__(self):
        self.cells: List[CellInfo] = []
        self.max_row = 0
        self.max_col = 0
    
    def parse_tablelabelme(self, data: Dict) -> None:
        """解析TableLabelMe格式数据"""
        self.cells = []
        
        for cell_data in data.get('cells', []):
            # 提取逻辑位置信息
            lloc = cell_data.get('lloc', {})
            
            # 提取内容信息
            content_list = cell_data.get('content', [])
            content_text = ' '.join([c.get('text', '') for c in content_list if c.get('text')])
            
            cell = CellInfo(
                cell_ind=cell_data.get('cell_ind', 0),
                start_row=lloc.get('start_row', 0),
                end_row=lloc.get('end_row', 0),
                start_col=lloc.get('start_col', 0),
                end_col=lloc.get('end_col', 0),
                content=content_text.strip(),
                is_header=cell_data.get('header', False),
                bbox=cell_data.get('bbox', {})
            )
            
            self.cells.append(cell)
            
            # 更新表格尺寸
            self.max_row = max(self.max_row, cell.end_row)
            self.max_col = max(self.max_col, cell.end_col)
        
        logger.info(f"解析完成：{len(self.cells)}个单元格，表格尺寸：{self.max_row+1}x{self.max_col+1}")
    
    def create_table_matrix(self) -> np.ndarray:
        """创建表格矩阵，处理跨行跨列单元格"""
        # 创建表格矩阵，初始值为-1表示空单元格
        matrix = np.full((self.max_row + 1, self.max_col + 1), -1, dtype=int)
        
        # 按单元格索引排序，确保处理顺序一致
        sorted_cells = sorted(self.cells, key=lambda x: x.cell_ind)
        
        for cell in sorted_cells:
            # 填充单元格占用的所有位置
            for r in range(cell.start_row, cell.end_row + 1):
                for c in range(cell.start_col, cell.end_col + 1):
                    if 0 <= r <= self.max_row and 0 <= c <= self.max_col:
                        matrix[r, c] = cell.cell_ind
        
        return matrix
    
    def generate_html_table(self) -> str:
        """生成HTML表格结构"""
        matrix = self.create_table_matrix()
        
        # 创建单元格索引映射
        cell_map = {cell.cell_ind: cell for cell in self.cells}
        
        # 创建HTML表格
        soup = BeautifulSoup('<table></table>', 'html.parser')
        table = soup.table
        
        # 记录已处理的单元格，避免重复添加
        processed_cells = set()
        
        for row_idx in range(self.max_row + 1):
            tr = soup.new_tag('tr')
            
            for col_idx in range(self.max_col + 1):
                cell_ind = matrix[row_idx, col_idx]
                
                # 跳过空单元格或已处理的单元格
                if cell_ind == -1 or cell_ind in processed_cells:
                    continue
                
                # 检查是否为跨行跨列单元格的起始位置
                cell = cell_map.get(cell_ind)
                if not cell or cell.start_row != row_idx or cell.start_col != col_idx:
                    continue
                
                # 创建单元格标签
                tag_name = 'th' if cell.is_header else 'td'
                td = soup.new_tag(tag_name)
                
                # 设置单元格内容
                if cell.content:
                    td.string = cell.content
                
                # 设置跨行跨列属性
                if cell.rowspan > 1:
                    td['rowspan'] = str(cell.rowspan)
                if cell.colspan > 1:
                    td['colspan'] = str(cell.colspan)
                
                # 添加自定义属性用于调试
                td['data-cell-ind'] = str(cell.cell_ind)
                
                tr.append(td)
                processed_cells.add(cell_ind)
            
            # 只有当行中有单元格时才添加行
            if tr.find_all(['td', 'th']):
                table.append(tr)
        
        return str(soup)
    
    def convert_to_html(self, tablelabelme_data: Dict) -> str:
        """主转换方法：TableLabelMe -> HTML"""
        try:
            self.parse_tablelabelme(tablelabelme_data)
            html_table = self.generate_html_table()
            
            logger.info("HTML转换完成")
            return html_table
            
        except Exception as e:
            logger.error(f"转换过程中发生错误：{str(e)}")
            raise

class TEDSCalculator:
    """TEDS计算器"""
    
    def __init__(self, structure_only: bool = False):
        """
        初始化TEDS计算器
        
        Args:
            structure_only: 是否只计算结构相似度（忽略内容）
        """
        self.structure_only = structure_only
        
        try:
            from table_recognition_metric import TEDS
            self.teds_engine = TEDS(structure_only=structure_only)
            logger.info(f"TEDS引擎初始化完成，structure_only={structure_only}")
        except ImportError:
            logger.warning("table_recognition_metric库未安装，将使用简化实现")
            self.teds_engine = None
    
    def calculate_teds(self, gt_html: str, pred_html: str) -> float:
        """
        计算TEDS分数
        
        Args:
            gt_html: 真实标签HTML
            pred_html: 预测结果HTML
            
        Returns:
            TEDS分数 (0-1之间)
        """
        if self.teds_engine:
            try:
                score = self.teds_engine(gt_html, pred_html)
                logger.info(f"TEDS计算完成，分数：{score:.4f}")
                return score
            except Exception as e:
                logger.error(f"TEDS计算失败：{str(e)}")
                return 0.0
        else:
            # 简化实现：基于HTML字符串相似度
            return self._simple_similarity(gt_html, pred_html)
    
    def _simple_similarity(self, html1: str, html2: str) -> float:
        """简化的相似度计算（当TEDS库不可用时）"""
        if html1 == html2:
            return 1.0
        
        # 基于编辑距离的简单实现
        from difflib import SequenceMatcher
        matcher = SequenceMatcher(None, html1, html2)
        return matcher.ratio()

def main():
    """主函数：演示使用方法"""
    # 示例数据（基于提供的JSON格式）
    sample_data = {
        "table_ind": 0,
        "cells": [
            {
                "cell_ind": 0,
                "header": True,
                "content": [{"text": "列1"}],
                "bbox": {"p1": [185.0, 167.0], "p2": [214.0, 167.0], "p3": [214.0, 188.0], "p4": [185.0, 188.0]},
                "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}
            },
            {
                "cell_ind": 1,
                "header": True,
                "content": [{"text": "列2"}],
                "bbox": {"p1": [214.0, 167.0], "p2": [296.0, 167.0], "p3": [296.0, 188.0], "p4": [214.0, 188.0]},
                "lloc": {"start_row": 0, "end_row": 0, "start_col": 1, "end_col": 1}
            },
            {
                "cell_ind": 2,
                "header": False,
                "content": [{"text": "数据1"}],
                "bbox": {"p1": [185.0, 188.0], "p2": [214.0, 188.0], "p3": [214.0, 209.0], "p4": [185.0, 209.0]},
                "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}
            },
            {
                "cell_ind": 3,
                "header": False,
                "content": [{"text": "数据2"}],
                "bbox": {"p1": [214.0, 188.0], "p2": [296.0, 188.0], "p3": [296.0, 209.0], "p4": [214.0, 209.0]},
                "lloc": {"start_row": 1, "end_row": 1, "start_col": 1, "end_col": 1}
            }
        ]
    }
    
    # 创建转换器
    converter = TableLabelMeToHTMLConverter()
    
    # 转换为HTML
    html_result = converter.convert_to_html(sample_data)
    print("转换结果：")
    print(html_result)
    
    # 计算TEDS（示例）
    calculator = TEDSCalculator(structure_only=True)
    teds_score = calculator.calculate_teds(html_result, html_result)
    print(f"\nTEDS分数：{teds_score}")

if __name__ == "__main__":
    main()
