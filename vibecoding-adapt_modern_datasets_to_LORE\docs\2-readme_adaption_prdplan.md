# LORE-TSR项目TableLabelMe数据格式支持需求规划

## 1. 项目概述

### 1.1 规划目标
为LORE-TSR项目增量支持TableLabelMe数据格式，在保持与原有COCO格式完全兼容的前提下，实现多源异构数据集的统一训练能力。核心目标是支持多源异构数据集的统一训练，提升数据集适配的灵活性和扩展性，为未来数据格式扩展奠定架构基础。

### 1.2 数据格式对比分析

#### 1.2.1 原有COCO格式特征
- **组织方式**：集中式存储，共享images目录，统一标注文件
- **标注格式**：扩展COCO格式，包含segmentation和logic_axis字段
- **文件结构**：
  ```
  ├── data_dir
  │   ├── images
  │   └── json
  │       ├──train.json
  │       └──test.json
  ```

#### 1.2.2 新增TableLabelMe格式特征
- **组织方式**：分布式存储，图像与标注文件一对一对应
- **标注格式**：TableLabelMe格式，使用bbox.p1-p4和lloc字段
- **文件结构**：
  ```
  src_dir/
  ├── part_0001/
  │   ├── xxx.jpg/png
  │   ├── xxx.json 或 xxx_table_annotation.json
  │   └── ...
  ├── part_0002/
  │   ├── xxx.jpg/png
  │   ├── xxx.json 或 xxx_table_annotation.json
  │   └── ...
  └── ...
  ```

#### 1.2.3 关键字段映射关系
| TableLabelMe字段 | LORE-TSR内部字段 | 转换逻辑 |
|-----------------|-----------------|---------|
| bbox.p1-p4 | segmentation | 提取四个角点坐标，按顺序组合成一维数组 |
| lloc | logic_axis | 将start_row, end_row, start_col, end_col组合 |
| cell_ind | annotation id | image_id + cell_ind生成全局唯一ID |
| 文件名关联 | image_id | 基于文件路径哈希生成唯一ID |
| quality | 数据筛选 | 只加载"合格"标记的样本 |
| table_ind, type, border, content等 | 额外信息保留 | 作为Dataset.__getitem__返回字典的额外字段 |

### 1.3 核心约束

#### 1.3.1 技术约束
- **算法一致性**：两种数据格式训练结果必须完全一致
- **无侵害性**：不修改原有的数据预处理、模型定义、损失函数逻辑
- **向后兼容**：不改变现有的训练和推理流程
- **接口一致性**：Dataset.__getitem__返回的数据结构保持兼容
- **性能保证**：保持与原项目完全相同的算法性能

#### 1.3.2 数据约束
- 用户保证训练集和验证集使用相同的数据格式
- 用户保证多源数据中不存在重复的图像文件
- TableLabelMe格式数据必须包含必要的字段（bbox、lloc、quality等）

#### 1.3.3 使用约束
- 配置文件路径必须使用绝对路径
- 数据目录结构必须符合规定的part_xxxx格式
- 标注文件命名必须与图像文件名对应（支持.json或_table_annotation.json后缀）

### 1.4 技术风险评估
- **最高风险**：算法一致性保证，需要严格验证格式转换的正确性
- **中等风险**：多源数据配置的复杂性，需要robust的错误处理机制
- **低风险**：配置系统扩展，基于现有参数体系进行增量修改

## 2. 迭代开发计划

### 迭代1：基础格式解析器（MVP）

**目标**：实现TableLabelMe格式的基础解析和转换功能

**核心功能**：
- 创建TableLabelMe格式解析器类 `TableLabelMeParser`
- 实现bbox.p1-p4到segmentation的坐标转换
- 实现lloc字段到logic_axis的结构转换
- 处理cell_ind和文件名关联生成全局唯一ID

**具体任务**：
1. **JSON结构解析**：
   - 解析TableLabelMe JSON文件的完整结构
   - 提取关键字段：bbox、lloc、cell_ind、quality、table_ind、type、border、content
   - 处理字段缺失和类型异常情况
   - 验证必要字段的存在性

2. **坐标转换算法**：
   - 提取bbox.p1、p2、p3、p4四个角点坐标
   - 按照顺序组合成一维segmentation数组：[p1.x, p1.y, p2.x, p2.y, p3.x, p3.y, p4.x, p4.y]
   - 确保坐标顺序与LORE-TSR内部格式一致
   - 处理坐标值异常（负数、超出图像边界等）

3. **逻辑结构转换**：
   - 提取lloc字段中的start_row、end_row、start_col、end_col
   - 组合为logic_axis格式：[start_row, end_row, start_col, end_col]
   - 验证逻辑坐标的合理性（start <= end）
   - 处理逻辑坐标缺失或异常情况

4. **ID生成策略**：
   - 基于图像文件路径生成稳定的image_id（使用哈希算法）
   - 结合image_id和cell_ind生成全局唯一的annotation_id
   - 确保ID生成的稳定性和唯一性
   - 处理cell_ind重复或缺失情况

5. **额外信息保留**：
   - 保留table_ind、type、border、content等原始字段
   - 作为额外信息添加到返回的数据字典中
   - 为未来功能扩展预留接口

**输入接口**：
- 单个TableLabelMe JSON文件路径
- 对应的图像文件路径
- 可选的配置参数（坐标验证阈值等）

**输出接口**：
- 标准化的数据字典，包含以下字段：
  ```python
  {
      'image_id': int,           # 基于文件路径生成的唯一ID
      'annotation_id': int,      # 全局唯一的标注ID
      'segmentation': list,      # 转换后的分割坐标
      'logic_axis': list,        # 转换后的逻辑坐标
      'bbox': list,              # 边界框坐标 [x, y, w, h]
      'area': float,             # 区域面积
      'category_id': int,        # 类别ID（固定为1，表示表格单元格）
      'extra_info': dict         # 额外信息字段
  }
  ```

**验收标准**：
- 单文件解析成功率100%（对于格式正确的文件）
- 转换后数据结构字段完整性检查通过
- 坐标转换数学正确性验证通过
- ID生成的唯一性和稳定性验证通过
- 异常情况处理验证通过

**技术约束**：
- 不依赖任何现有LORE-TSR模块，保持独立性
- 异常处理：JSON格式错误时返回None而非抛出异常
- 性能要求：单文件解析时间 < 10ms
- 内存使用：解析过程中内存占用合理

---

### 迭代2：数据集扫描和索引构建

**目标**：实现完整TableLabelMe数据集的目录扫描和文件索引构建

**核心功能**：
- 递归扫描part_xxxx目录结构
- 构建图像-标注文件映射关系
- 生成全局唯一的image_id标识
- 支持多源数据路径配置

**具体任务**：
1. **目录遍历算法**：
   - 实现递归目录扫描，识别符合part_xxxx模式的子目录
   - 支持正则表达式匹配：part_\d{4}或类似模式
   - 处理目录权限异常和访问错误
   - 记录扫描统计信息（目录数量、文件数量等）

2. **文件识别和匹配**：
   - 扫描图像文件：支持.jpg、.jpeg、.png格式
   - 扫描标注文件：支持.json和_table_annotation.json两种命名模式
   - 实现文件名匹配逻辑：
     ```
     image: xxx.jpg -> annotation: xxx.json 或 xxx_table_annotation.json
     image: yyy.png -> annotation: yyy.json 或 yyy_table_annotation.json
     ```
   - 处理文件名特殊字符和编码问题

3. **image_id生成算法**：
   - 基于完整文件路径的哈希算法（MD5或SHA256）
   - 确保相同文件路径始终生成相同ID
   - 处理路径分隔符差异（Windows/Linux）
   - 生成32位或64位整数ID，避免冲突

4. **索引数据结构构建**：
   - 构建完整的文件映射字典
   - 数据结构设计：
     ```python
     file_index = {
         image_id: {
             "image_path": str,      # 图像文件绝对路径
             "annotation_path": str, # 标注文件绝对路径
             "part_dir": str,        # 所属part目录
             "dataset_source": str   # 数据源标识
         }
     }
     ```
   - 添加反向索引：路径到ID的映射
   - 构建统计信息字典

5. **多源数据支持**：
   - 支持配置文件中的多个数据路径
   - 为每个数据源添加标识信息
   - 处理不同数据源间的ID冲突
   - 合并多源数据的索引结果

**输入接口**：
- 数据根目录路径列表（支持多源数据）
- 可选的扫描配置参数：
  ```python
  scan_config = {
      "part_pattern": r"part_\d{4}",     # part目录匹配模式
      "image_extensions": [".jpg", ".jpeg", ".png"],  # 支持的图像格式
      "annotation_patterns": [".json", "_table_annotation.json"],  # 标注文件模式
      "max_depth": 3,                    # 最大扫描深度
      "follow_symlinks": False           # 是否跟随符号链接
  }
  ```

**输出接口**：
- 完整的文件索引字典
- 详细的统计信息：
  ```python
  statistics = {
      "total_directories": int,          # 扫描的总目录数
      "part_directories": int,           # 有效的part目录数
      "total_images": int,               # 发现的图像文件总数
      "total_annotations": int,          # 发现的标注文件总数
      "valid_pairs": int,                # 有效的图像-标注对数
      "orphan_images": int,              # 无对应标注的图像数
      "orphan_annotations": int,         # 无对应图像的标注数
      "duplicate_ids": int,              # ID冲突数量
      "scan_time": float                 # 扫描耗时（秒）
  }
  ```

**验收标准**：
- 能够正确识别所有有效的图像-标注文件对
- image_id生成的唯一性验证：无ID冲突
- image_id生成的稳定性验证：多次扫描结果一致
- 处理10万+文件规模的性能测试通过（扫描时间 < 60秒）
- 多源数据合并功能正常
- 异常目录和文件的处理验证通过

**技术约束**：
- 内存使用：索引构建过程中内存占用 < 1GB（对于10万文件）
- 性能要求：单个文件的处理时间 < 1ms
- 并发安全：支持多线程扫描（可选）
- 错误恢复：单个文件或目录异常不影响整体扫描

---

### 迭代3：质量筛选和错误处理

**目标**：实现数据质量控制机制和完善的异常处理

**核心功能**：
- 基于quality字段的数据筛选
- 文件缺失和格式异常的处理
- 详细的日志记录机制
- 异常情况的统计和报告

**具体任务**：
1. **质量筛选机制**：
   - 解析标注文件中的"quality"字段（位于JSON顶层）
   - 只保留quality为"合格"的样本参与训练
   - 支持可配置的质量标准：
     ```python
     quality_config = {
         "accepted_values": ["合格", "qualified", "good"],  # 接受的质量标记
         "case_sensitive": False,                          # 是否区分大小写
         "default_quality": "unknown"                      # 缺失时的默认值
     }
     ```
   - 记录跳过样本的详细信息和原因

2. **异常情况处理**：
   - **图像文件无对应标注文件**：跳过该样本，记录到孤儿图像列表
   - **标注文件无对应图像文件**：跳过该样本，记录到孤儿标注列表
   - **数据目录不存在或为空**：跳过该目录，继续处理其他目录
   - **文件权限问题**：记录权限错误，尝试跳过
   - **文件损坏或无法读取**：记录文件损坏信息，跳过处理
   - **JSON格式异常**：
     - 语法错误：记录解析失败信息
     - 字段缺失：记录缺失字段列表
     - 数据类型错误：记录类型不匹配信息
     - 编码问题：尝试多种编码格式

3. **分级日志系统**：
   - **INFO级别**：
     - 数据加载开始和完成信息
     - 统计信息：总文件数、有效文件数、跳过文件数
     - 处理进度信息（每处理1000个文件输出一次）
   - **WARNING级别**：
     - 可恢复的异常：文件缺失、质量不合格
     - 数据格式问题：字段缺失但可以使用默认值
     - 性能警告：处理时间过长的文件
   - **ERROR级别**：
     - 严重错误：无法恢复的文件损坏
     - 配置错误：路径不存在、权限不足
     - 系统错误：内存不足、磁盘空间不足

4. **日志格式规范**：
   - 时间戳：精确到毫秒
   - 文件路径：完整的绝对路径
   - 错误类型：标准化的错误代码
   - 详细信息：具体的错误描述
   - 示例格式：
     ```
     [2025-01-21 10:30:45.123] INFO  [TableLabelMe] 开始扫描数据集: /path/to/dataset
     [2025-01-21 10:30:45.456] WARN  [TableLabelMe] 跳过质量不合格样本: /path/to/image.jpg (quality: 不合格)
     [2025-01-21 10:30:45.789] ERROR [TableLabelMe] JSON解析失败: /path/to/annotation.json (语法错误: line 15)
     ```

5. **异常统计和报告**：
   - 构建详细的异常统计数据结构
   - 生成可读的异常报告
   - 提供异常样本的详细列表
   - 支持异常数据的导出和分析

**输入接口**：
- 原始文件索引（来自迭代2）
- 质量筛选配置参数
- 日志配置参数

**输出接口**：
- 过滤后的有效数据索引
- 详细的异常统计报告：
  ```python
  exception_report = {
      "quality_filtered": {
          "count": int,
          "samples": [{"path": str, "quality": str, "reason": str}]
      },
      "file_missing": {
          "orphan_images": [{"path": str, "reason": str}],
          "orphan_annotations": [{"path": str, "reason": str}]
      },
      "format_errors": {
          "json_syntax_errors": [{"path": str, "error": str, "line": int}],
          "missing_fields": [{"path": str, "missing_fields": list}],
          "type_errors": [{"path": str, "field": str, "expected": str, "actual": str}]
      },
      "file_access_errors": [{"path": str, "error": str}],
      "summary": {
          "total_processed": int,
          "valid_samples": int,
          "filtered_samples": int,
          "error_samples": int,
          "success_rate": float
      }
  }
  ```
- 结构化日志输出（同时输出到控制台和日志文件）

**验收标准**：
- 异常情况下程序稳定运行，不崩溃
- 单个文件异常不影响整体处理流程
- 日志记录完整准确，便于问题排查
- 质量筛选逻辑正确，统计数据准确
- 异常报告信息完整，便于数据质量分析
- 处理10万文件的异常处理性能测试通过

**技术约束**：
- 错误恢复：单个文件异常不影响整体处理流程
- 日志格式：与现有LORE-TSR日志系统保持一致
- 内存使用：异常信息存储不超过100MB
- 性能要求：异常处理不显著影响整体处理速度
- 线程安全：日志记录支持多线程环境

---

### 迭代4：配置系统集成

**目标**：将TableLabelMe支持集成到现有的LORE-TSR配置系统

**核心功能**：
- 扩展dataset参数识别逻辑
- 新增data_config参数支持
- 实现参数兼容性处理
- 创建多源数据配置机制

**具体任务**：
1. **参数识别逻辑扩展**：
   - 修改现有参数解析逻辑，支持新的参数组合：
     ```bash
     --dataset table --dataset_name TableLabelMe
     ```
   - 在现有的dataset参数基础上，增加对"table"值的支持
   - 在现有的dataset_name参数基础上，增加对"TableLabelMe"值的支持
   - 实现参数组合验证：确保dataset和dataset_name的组合有效

2. **data_config参数支持**：
   - 新增--data_config参数，接受配置文件的绝对路径
   - 参数定义：
     ```python
     parser.add_argument('--data_config', type=str, default=None,
                        help='Path to dataset configuration file (absolute path)')
     ```
   - 支持配置文件路径验证：检查文件存在性和可读性
   - 实现配置文件的动态加载和解析

3. **配置文件模板创建**：
   - 创建configs/dataset_configs.py配置文件模板
   - 配置文件结构设计：
     ```python
     DATASET_PATH_CONFIGS = {
         'tableme_full': {
             'train': [
                 '/path/to/TabRecSet_chinese/train',
                 '/path/to/TabRecSet_english/train',
                 '/path/to/WTW/train',
                 '/path/to/TALOCRTable/train'
             ],
             'val': [
                 '/path/to/TabRecSet_chinese/val',
                 '/path/to/TabRecSet_english/val',
                 '/path/to/WTW/val',
                 '/path/to/TALOCRTable/val'
             ]
         },
         'tableme_chinese_only': {
             'train': ['/path/to/TabRecSet_chinese/train'],
             'val': ['/path/to/TabRecSet_chinese/val']
         },
         'tableme_english_only': {
             'train': ['/path/to/TabRecSet_english/train'],
             'val': ['/path/to/TabRecSet_english/val']
         }
     }
     ```
   - 支持配置验证：路径存在性检查、权限验证
   - 提供配置文件示例和注释

4. **参数兼容性处理**：
   - 实现参数模式检测：
     ```python
     def detect_dataset_mode(args):
         if args.dataset == 'table' and args.dataset_name == 'TableLabelMe':
             return 'TableLabelMe'
         else:
             return 'COCO'
     ```
   - TableLabelMe模式下的参数处理：
     - 忽略--image_dir和--anno_path参数
     - 必须提供--data_config参数
     - 验证data_config指向的配置文件
   - COCO模式下的参数处理：
     - 保持原有逻辑不变
     - 忽略--data_config参数（如果提供）
   - 参数冲突检测和提示

5. **多源数据路径配置**：
   - 实现配置文件解析逻辑
   - 支持train/val数据集的分别配置
   - 支持多个数据源路径的合并
   - 路径规范化：转换为绝对路径，处理路径分隔符
   - 配置验证：检查所有路径的有效性

**输入接口**：
- 扩展后的命令行参数集
- 外部配置文件（dataset_configs.py）
- 示例命令行：
  ```bash
  python main.py ctdet_mid \
      --dataset table \
      --exp_id train_wireless \
      --dataset_name TableLabelMe \
      --data_config /absolute/path/to/configs/dataset_configs.py \
      --wiz_2dpe \
      --wiz_stacking \
      --tsfm_layers 4 \
      --stacking_layers 4 \
      --batch_size 6 \
      --master_batch 6 \
      --arch resfpnhalf_18 \
      --lr 1e-4 \
      --K 500 \
      --MK 1000 \
      --num_epochs 200 \
      --lr_step '100, 160' \
      --gpus 0 \
      --num_workers 16 \
      --val_intervals 5
  ```

**输出接口**：
- 统一的配置对象，包含以下信息：
  ```python
  config = {
      'dataset_mode': str,              # 'TableLabelMe' 或 'COCO'
      'data_paths': {
          'train': [str],               # 训练数据路径列表
          'val': [str]                  # 验证数据路径列表
      },
      'dataset_config_name': str,       # 配置名称（如'tableme_full'）
      'original_args': Namespace        # 原始命令行参数
  }
  ```
- 参数验证结果和错误提示
- 配置加载日志信息

**验收标准**：
- 命令行参数解析正确，支持新旧两种模式
- 配置文件加载和验证功能正常
- 参数冲突检测和提示机制有效
- 多源数据路径配置功能正常
- 向后兼容性验证：原有COCO格式命令行完全正常
- 配置文件路径验证功能正常

**技术约束**：
- 向后兼容：原有COCO格式的所有参数组合仍然有效
- 配置隔离：TableLabelMe配置不影响COCO格式的参数处理
- 路径安全：只接受绝对路径，防止相对路径引起的问题
- 配置验证：严格验证配置文件格式和内容的正确性

---

### 迭代5：训练流程集成和兼容性验证

**目标**：确保TableLabelMe格式在完整训练流程中正常工作

**核心功能**：
- 创建TableLabelMe Dataset类
- 集成到现有数据加载工厂
- 确保训练流程无缝运行
- 验证算法一致性

**具体任务**：
1. **TableLabelMeDataset类创建**：
   - 继承现有Dataset基类，保持接口一致性
   - 类定义结构：
     ```python
     class TableLabelMeDataset(Dataset):
         def __init__(self, opt, split):
             super(TableLabelMeDataset, self).__init__()
             self.opt = opt
             self.split = split
             self.parser = TableLabelMeParser()  # 来自迭代1
             self.file_index = self._build_index()  # 来自迭代2
             self.valid_samples = self._filter_samples()  # 来自迭代3

         def __len__(self):
             return len(self.valid_samples)

         def __getitem__(self, index):
             # 返回与COCO格式完全兼容的数据结构
             pass
     ```

2. **__getitem__方法实现**：
   - 返回与COCO格式完全兼容的数据结构
   - 数据结构定义：
     ```python
     sample = {
         'input': torch.Tensor,        # 输入图像张量
         'hm': torch.Tensor,           # 热图标签
         'reg': torch.Tensor,          # 回归标签
         'reg_mask': torch.Tensor,     # 回归掩码
         'ind': torch.Tensor,          # 索引
         'wh': torch.Tensor,           # 宽高
         'logic_axis': torch.Tensor,   # 逻辑坐标
         'meta': dict                  # 元数据信息
     }
     ```
   - 确保所有张量的形状和数据类型与COCO格式一致
   - 集成数据预处理流程：图像变换、标注转换、数据增强

3. **数据预处理集成**：
   - 复用现有的图像预处理流程：
     - 图像读取和解码
     - 尺寸调整和填充
     - 数据增强（旋转、翻转、颜色变换等）
     - 归一化处理
   - 复用现有的标注预处理流程：
     - 坐标变换（原始坐标到网络输入坐标）
     - 热图生成
     - 回归目标计算
     - 掩码生成
   - 确保预处理流程与COCO格式完全一致

4. **工厂函数集成**：
   - 修改get_dataset工厂函数，支持TableLabelMe格式：
     ```python
     def get_dataset(dataset, task):
         if dataset == 'coco':
             return COCODataset
         elif dataset == 'table' and task == 'TableLabelMe':
             return TableLabelMeDataset
         else:
             raise ValueError(f'Unsupported dataset: {dataset}')
     ```
   - 保持原有COCO格式的创建逻辑不变
   - 添加参数验证和错误提示

5. **性能优化**：
   - 实现数据缓存机制（可选）
   - 优化文件I/O操作
   - 支持多进程数据加载
   - 内存使用优化

6. **算法一致性验证**：
   - 设计对比实验：相同数据的两种格式训练
   - 验证指标：
     - 训练损失曲线一致性
     - 验证指标一致性
     - 模型权重一致性（在相同初始化下）
     - 推理结果一致性
   - 建立自动化验证流程

**输入接口**：
- 配置参数（来自迭代4）
- 数据路径信息
- 训练/验证模式标识

**输出接口**：
- 可用于训练的Dataset实例
- 与COCO格式完全兼容的数据接口
- 性能统计信息：
  ```python
  performance_stats = {
      'dataset_size': int,              # 数据集大小
      'avg_load_time': float,           # 平均加载时间
      'memory_usage': float,            # 内存使用量
      'cache_hit_rate': float           # 缓存命中率（如果启用）
  }
  ```

**验收标准**：
- **功能验收**：
  - 完整训练流程运行成功，无报错
  - Dataset.__getitem__返回数据结构与COCO格式完全一致
  - 数据加载器正常工作，支持多进程
  - 所有数据预处理流程正常
- **性能验收**：
  - 数据加载性能不显著下降（< 10%）
  - 内存使用合理，无内存泄漏
  - 模型收敛正常，训练指标合理
- **一致性验收**：
  - 算法一致性验证：相同数据的两种格式训练结果一致
  - 推理结果一致性验证通过
  - 数据统计一致性验证通过

**技术约束**：
- 接口兼容：Dataset.__getitem__返回结构与COCO格式完全一致
- 性能要求：数据加载性能不显著下降
- 内存使用：合理的内存占用，支持大数据集训练
- 线程安全：支持多进程数据加载
- 错误处理：数据加载异常不影响训练流程

---

### 迭代6：可视化验证工具

**目标**：提供格式转换正确性的可视化验证工具

**核心功能**：
- 独立的可视化验证脚本
- 批量处理和对比展示
- 转换正确性验证报告
- 异常样本分析

**具体任务**：
1. **独立可视化脚本创建**：
   - 创建visualize_conversion.py脚本
   - 脚本结构设计：
     ```python
     def main():
         args = parse_arguments()
         visualizer = TableLabelMeVisualizer(args)
         visualizer.run_batch_visualization()
         visualizer.generate_report()

     class TableLabelMeVisualizer:
         def __init__(self, config):
             self.config = config
             self.parser = TableLabelMeParser()

         def visualize_single_sample(self, image_path, annotation_path):
             # 单样本可视化
             pass

         def run_batch_visualization(self):
             # 批量处理
             pass

         def generate_report(self):
             # 生成验证报告
             pass
     ```

2. **批量处理功能**：
   - 支持完整数据集的批量验证
   - 可配置的采样策略：
     - 随机采样：从数据集中随机选择N个样本
     - 均匀采样：从每个part目录中均匀选择样本
     - 全量处理：处理所有样本（用于小数据集）
   - 进度显示和中断恢复功能
   - 并行处理支持（多进程）

3. **对比可视化生成**：
   - 原始TableLabelMe标注可视化：
     - 在原图上绘制bbox.p1-p4四个角点
     - 用不同颜色标注不同的单元格
     - 显示lloc信息（行列坐标）
     - 显示quality、type等额外信息
   - 转换后LORE-TSR格式可视化：
     - 在原图上绘制转换后的segmentation
     - 用相同颜色标注对应的单元格
     - 显示logic_axis信息
     - 显示转换后的ID信息
   - 并排对比展示：
     - 左侧：原始TableLabelMe标注
     - 右侧：转换后LORE-TSR标注
     - 底部：差异统计和验证结果

4. **可视化样式配置**：
   - 颜色方案：支持多种颜色主题
   - 字体设置：标注文字的字体和大小
   - 线条样式：边框线条的粗细和样式
   - 透明度：标注覆盖的透明度设置
   - 输出格式：支持PNG、JPG、PDF等格式

5. **验证报告生成**：
   - 转换正确性统计：
     ```python
     validation_report = {
         "summary": {
             "total_samples": int,
             "successful_conversions": int,
             "failed_conversions": int,
             "accuracy_rate": float
         },
         "coordinate_validation": {
             "bbox_accuracy": float,          # 边界框坐标准确率
             "segmentation_accuracy": float,  # 分割坐标准确率
             "logic_axis_accuracy": float     # 逻辑坐标准确率
         },
         "visual_validation": {
             "visual_match_rate": float,      # 视觉匹配率
             "annotation_completeness": float # 标注完整性
         },
         "error_analysis": {
             "coordinate_errors": [dict],     # 坐标转换错误
             "missing_annotations": [dict],   # 缺失标注
             "format_errors": [dict]          # 格式错误
         }
     }
     ```
   - HTML格式的详细报告
   - 异常样本的详细列表和分析

6. **命令行接口设计**：
   - 支持灵活的命令行参数：
     ```bash
     python visualize_conversion.py \
         --data_config /path/to/dataset_configs.py \
         --config_name tableme_full \
         --output_dir /path/to/output \
         --sample_count 100 \
         --sample_strategy random \
         --output_format png \
         --parallel_workers 4 \
         --generate_report
     ```

**输入接口**：
- TableLabelMe数据集路径（通过配置文件）
- 可视化配置参数：
  ```python
  visualization_config = {
      "sample_count": int,              # 采样数量
      "sample_strategy": str,           # 采样策略
      "output_directory": str,          # 输出目录
      "output_format": str,             # 输出格式
      "color_scheme": str,              # 颜色方案
      "font_size": int,                 # 字体大小
      "line_width": int,                # 线条宽度
      "transparency": float,            # 透明度
      "parallel_workers": int           # 并行工作进程数
  }
  ```

**输出接口**：
- 可视化对比图像文件（按样本组织）
- HTML格式的验证报告
- JSON格式的详细统计数据
- 异常样本列表和错误日志

**验收标准**：
- **功能验收**：
  - 视觉确认转换前后标注完全一致
  - 批量处理功能稳定可靠
  - 验证报告信息完整准确
  - 命令行接口易用性良好
- **性能验收**：
  - 单样本可视化时间 < 5秒
  - 批量处理支持中断和恢复
  - 并行处理功能正常
- **质量验收**：
  - 可视化图像清晰，标注准确
  - 报告内容详实，便于分析
  - 异常检测准确率 > 95%

**技术约束**：
- 独立性：不依赖训练环境，可独立运行
- 可扩展性：支持未来其他格式的可视化验证
- 跨平台：支持Windows、Linux、macOS
- 依赖最小化：只依赖必要的图像处理库（PIL、OpenCV等）

## 3. 整体验收标准

### 3.1 基本功能验收
- **数据加载功能**：
  - 能够成功加载TableLabelMe格式数据集
  - 支持多源数据路径配置
  - 质量筛选机制正常工作
  - 数据统计和索引构建正确
- **训练流程功能**：
  - 能够完成完整的训练流程
  - 训练过程中的数据统计和日志记录正确
  - 支持多GPU训练和分布式训练
  - 模型保存和加载功能正常
- **推理功能**：
  - 支持使用TableLabelMe格式训练的模型进行推理
  - 推理结果格式与COCO格式一致
  - 推理性能满足要求

### 3.2 兼容性验收
- **向后兼容性**：
  - 原有COCO格式功能完全不受影响
  - 所有现有训练和推理脚本正常工作
  - 原有命令行参数组合仍然有效
  - 原有配置文件仍然可用
- **接口兼容性**：
  - Dataset.__getitem__返回结构与COCO格式完全一致
  - 数据预处理流程保持一致
  - 模型输入输出格式保持一致
- **参数兼容性**：
  - 参数体系向后兼容
  - 新增参数不影响原有功能
  - 参数冲突检测和提示机制有效

### 3.3 算法一致性验收
- **训练一致性**：
  - 两种格式训练的模型在相同数据上性能一致
  - 训练损失曲线一致性验证通过
  - 验证指标一致性验证通过
  - 模型收敛行为一致
- **推理一致性**：
  - 相同数据的推理结果完全一致
  - 推理性能指标一致
  - 输出格式完全一致
- **数据一致性**：
  - 格式转换的数学正确性验证通过
  - 坐标变换的精度验证通过
  - 数据统计的一致性验证通过

### 3.4 质量验收
- **可视化验证**：
  - 可视化验证工具确认格式转换的正确性
  - 视觉效果一致性验证通过
  - 异常样本检测准确率 > 95%
- **错误处理验证**：
  - 错误处理机制能够正确处理各种异常情况
  - 异常情况下程序稳定运行，不崩溃
  - 错误恢复机制有效
- **日志质量验证**：
  - 日志记录完整准确，便于问题排查
  - 日志格式与现有系统一致
  - 日志级别设置合理

### 3.5 性能验收
- **数据加载性能**：
  - 数据加载性能不显著下降（< 10%）
  - 大数据集扫描时间在可接受范围内
  - 内存使用合理，无内存泄漏
- **训练性能**：
  - 训练速度不显著下降
  - GPU利用率保持正常
  - 内存占用合理
- **可扩展性**：
  - 支持大规模数据集（10万+样本）
  - 支持多进程数据加载
  - 支持分布式训练

### 3.6 可维护性验收
- **代码质量**：
  - 代码结构清晰，易于理解
  - 模块化设计，便于扩展
  - 代码注释完整，符合规范
- **文档完整性**：
  - 使用文档完整，示例清晰
  - API文档详细，接口说明准确
  - 故障排除指南实用
- **扩展性**：
  - 架构设计支持未来格式扩展
  - 接口设计灵活，易于扩展
  - 配置系统可扩展

## 4. 使用场景详细说明

### 4.1 训练场景
- **完整训练命令示例**：
  ```bash
  python main.py ctdet_mid \
      --dataset table \
      --exp_id train_wireless \
      --dataset_name TableLabelMe \
      --data_config /absolute/path/to/configs/dataset_configs.py \
      --wiz_2dpe \
      --wiz_stacking \
      --tsfm_layers 4 \
      --stacking_layers 4 \
      --batch_size 6 \
      --master_batch 6 \
      --arch resfpnhalf_18 \
      --lr 1e-4 \
      --K 500 \
      --MK 1000 \
      --num_epochs 200 \
      --lr_step '100, 160' \
      --gpus 0 \
      --num_workers 16 \
      --val_intervals 5
  ```
- **参数说明**：
  - `--dataset table`：指定数据集类型为table
  - `--dataset_name TableLabelMe`：指定具体的数据格式
  - `--data_config`：指向配置文件的绝对路径
  - 其他参数与原COCO格式保持一致

### 4.2 推理场景
- **推理命令示例**：
  ```bash
  python demo.py ctdet_mid \
      --dataset table \
      --dataset_name TableLabelMe \
      --load_model /path/to/trained/model.pth \
      --input_dir /path/to/test/images \
      --output_dir /path/to/results
  ```
- **要求**：
  - 需要指定相应的数据格式参数
  - 推理流程与原有COCO格式保持一致
  - 支持批量推理和单张图像推理

### 4.3 可视化验证场景
- **验证命令示例**：
  ```bash
  python visualize_conversion.py \
      --data_config /path/to/dataset_configs.py \
      --config_name tableme_full \
      --output_dir /path/to/visualization_output \
      --sample_count 100 \
      --sample_strategy random \
      --generate_report
  ```
- **功能**：
  - 独立运行可视化验证脚本
  - 批量处理指定数据集
  - 生成格式转换正确性验证报告

## 5. 技术交付物

### 5.1 代码交付
- **核心模块**：
  - `src/lib/datasets/table_labelme_parser.py`：TableLabelMe格式解析器
  - `src/lib/datasets/table_labelme_dataset.py`：TableLabelMe数据集类
  - `src/lib/datasets/dataset_factory.py`：扩展的数据集工厂（修改）
  - `src/lib/opts.py`：扩展的参数解析（修改）
- **配置模块**：
  - `configs/dataset_configs.py`：数据集配置文件模板
  - `configs/table_labelme_config_example.py`：配置示例文件
- **验证工具**：
  - `tools/visualize_conversion.py`：可视化验证脚本
  - `tools/validate_consistency.py`：算法一致性验证脚本
  - `tools/performance_benchmark.py`：性能对比测试工具

### 5.2 文档交付
- **使用指南**：
  - `docs/TableLabelMe_Usage_Guide.md`：TableLabelMe格式支持使用指南
  - `docs/Configuration_Guide.md`：配置文件使用指南
  - `docs/Migration_Guide.md`：从COCO格式迁移指南
- **技术文档**：
  - `docs/Format_Comparison.md`：COCO格式与TableLabelMe格式对比说明
  - `docs/API_Reference.md`：API接口参考文档
  - `docs/Troubleshooting_Guide.md`：故障排除指南
- **示例文档**：
  - `examples/training_examples.md`：训练命令示例
  - `examples/inference_examples.md`：推理命令示例
  - `examples/configuration_examples.md`：配置文件示例

### 5.3 测试交付
- **验证报告**：
  - `reports/format_conversion_validation.html`：格式转换正确性验证报告
  - `reports/compatibility_test_report.html`：兼容性测试报告
  - `reports/performance_comparison_report.html`：性能对比测试报告
  - `reports/algorithm_consistency_report.html`：算法一致性验证报告
- **测试脚本**：
  - `tests/test_table_labelme_parser.py`：解析器单元测试
  - `tests/test_table_labelme_dataset.py`：数据集类单元测试
  - `tests/test_integration.py`：集成测试脚本
  - `tests/test_performance.py`：性能测试脚本

### 5.4 配置模板
- **数据集配置模板**：
  ```python
  # configs/dataset_configs.py
  DATASET_PATH_CONFIGS = {
      'tableme_full': {
          'train': [
              '/path/to/TabRecSet_chinese/train',
              '/path/to/TabRecSet_english/train',
              '/path/to/WTW/train',
              '/path/to/TALOCRTable/train'
          ],
          'val': [
              '/path/to/TabRecSet_chinese/val',
              '/path/to/TabRecSet_english/val',
              '/path/to/WTW/val',
              '/path/to/TALOCRTable/val'
          ]
      }
  }
  ```
- **训练脚本模板**：
  - `scripts/train_table_labelme.sh`：TableLabelMe格式训练脚本
  - `scripts/eval_table_labelme.sh`：TableLabelMe格式评估脚本

## 6. 项目里程碑和时间规划

### 6.1 开发里程碑
- **里程碑1**：迭代1-3完成（基础功能）
  - 格式解析器、数据扫描、质量筛选功能完成
  - 预计开发时间：2-3周
- **里程碑2**：迭代4-5完成（系统集成）
  - 配置系统集成、训练流程集成完成
  - 预计开发时间：2-3周
- **里程碑3**：迭代6完成（验证工具）
  - 可视化验证工具完成
  - 预计开发时间：1-2周
- **里程碑4**：整体验收和文档完善
  - 所有验收标准通过，文档完善
  - 预计时间：1周

### 6.2 风险控制
- **技术风险**：每个迭代都有独立的验收标准，降低技术风险
- **进度风险**：迭代式开发，可以根据实际情况调整优先级
- **质量风险**：每个迭代都有完整的测试和验证机制

---

**文档版本**：v2.0
**创建日期**：2025年1月21日
**最后更新**：2025年1月21日
**规划完成**：2025年1月21日
