#!/usr/bin/env python3
"""
测试导入修复

验证main.py中的os模块导入问题是否已修复
"""

import sys
import os

# 添加src路径
sys.path.append('src')

def test_main_import():
    """测试main.py的导入"""
    print("🧪 测试main.py导入修复")
    
    try:
        # 尝试导入main模块
        import main
        print("✅ main.py导入成功")
        
        # 检查os模块是否可用
        if hasattr(main, 'os'):
            print("✅ os模块在main中可用")
        else:
            print("⚠️  os模块在main中不可用，但这可能是正常的")
        
        return True
        
    except Exception as e:
        print(f"❌ main.py导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_os_access():
    """测试os模块访问"""
    print("\n🧪 测试os模块访问")
    
    try:
        # 测试基本的os操作
        current_dir = os.getcwd()
        print(f"✅ 当前目录: {current_dir}")
        
        # 测试环境变量设置
        os.environ['TEST_VAR'] = 'test_value'
        test_val = os.environ.get('TEST_VAR')
        print(f"✅ 环境变量测试: {test_val}")
        
        # 清理测试变量
        del os.environ['TEST_VAR']
        
        return True
        
    except Exception as e:
        print(f"❌ os模块访问失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始导入修复验证测试")
    print("=" * 40)
    
    test_results = []
    
    test_results.append(test_main_import())
    test_results.append(test_os_access())
    
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    
    test_names = ["main.py导入", "os模块访问"]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {name}: {status}")
    
    if all(test_results):
        print("\n🎉 导入修复验证成功！")
        print("💡 现在可以尝试运行训练命令")
        return 0
    else:
        print("\n⚠️  部分测试失败")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
