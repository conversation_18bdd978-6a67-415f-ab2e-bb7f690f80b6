#!/usr/bin/env python3
"""
验证脚本 - 步骤3.3：完善异常处理和报告机制测试
测试QualityFilter模块的增强异常处理功能
"""

import sys
import os
import json
import tempfile

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_comprehensive_test_data(temp_dir: str):
    """创建包含各种异常情况的综合测试数据"""
    test_files = []
    
    # 1. 正常的合格文件
    image1 = os.path.join(temp_dir, "normal.jpg")
    annotation1 = os.path.join(temp_dir, "normal.json")
    with open(image1, 'w') as f:
        f.write("fake image data")
    with open(annotation1, 'w', encoding='utf-8') as f:
        json.dump({
            "quality": "合格",
            "bbox": {"p1": [0, 0], "p2": [100, 0], "p3": [100, 50], "p4": [0, 50]},
            "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}
        }, f, ensure_ascii=False)
    test_files.append((1, {"image_path": image1, "annotation_path": annotation1}))
    
    # 2. 缺失quality字段的文件
    image2 = os.path.join(temp_dir, "missing_quality.jpg")
    annotation2 = os.path.join(temp_dir, "missing_quality.json")
    with open(image2, 'w') as f:
        f.write("fake image data")
    with open(annotation2, 'w', encoding='utf-8') as f:
        json.dump({
            "bbox": {"p1": [0, 0], "p2": [100, 0], "p3": [100, 50], "p4": [0, 50]},
            "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}
        }, f, ensure_ascii=False)
    test_files.append((2, {"image_path": image2, "annotation_path": annotation2}))
    
    # 3. quality字段类型错误的文件
    image3 = os.path.join(temp_dir, "wrong_type.jpg")
    annotation3 = os.path.join(temp_dir, "wrong_type.json")
    with open(image3, 'w') as f:
        f.write("fake image data")
    with open(annotation3, 'w', encoding='utf-8') as f:
        json.dump({
            "quality": 123,  # 应该是字符串，但这里是数字
            "bbox": {"p1": [0, 0], "p2": [100, 0], "p3": [100, 50], "p4": [0, 50]}
        }, f, ensure_ascii=False)
    test_files.append((3, {"image_path": image3, "annotation_path": annotation3}))
    
    # 4. 缺失图像文件
    missing_image = os.path.join(temp_dir, "missing_image.jpg")
    annotation4 = os.path.join(temp_dir, "annotation4.json")
    with open(annotation4, 'w', encoding='utf-8') as f:
        json.dump({"quality": "合格"}, f, ensure_ascii=False)
    test_files.append((4, {"image_path": missing_image, "annotation_path": annotation4}))
    
    # 5. JSON格式错误的文件
    image5 = os.path.join(temp_dir, "bad_json.jpg")
    annotation5 = os.path.join(temp_dir, "bad_json.json")
    with open(image5, 'w') as f:
        f.write("fake image data")
    # 创建明确的JSON语法错误
    bad_json_content = '{"quality": "合格", "bbox": {'
    with open(annotation5, 'w', encoding='utf-8') as f:
        f.write(bad_json_content)
    test_files.append((5, {"image_path": image5, "annotation_path": annotation5}))
    
    # 6. 不合格质量标记的文件
    image6 = os.path.join(temp_dir, "unqualified.jpg")
    annotation6 = os.path.join(temp_dir, "unqualified.json")
    with open(image6, 'w') as f:
        f.write("fake image data")
    with open(annotation6, 'w', encoding='utf-8') as f:
        json.dump({
            "quality": "不合格",
            "bbox": {"p1": [0, 0], "p2": [100, 0], "p3": [100, 50], "p4": [0, 50]}
        }, f, ensure_ascii=False)
    test_files.append((6, {"image_path": image6, "annotation_path": annotation6}))
    
    return dict(test_files)

def test_enhanced_exception_handling():
    """测试增强的异常处理功能"""
    try:
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        print("=== 测试增强异常处理功能 ===")
        print()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建综合测试数据
            test_index = create_comprehensive_test_data(temp_dir)
            print(f"1. 创建综合测试数据: {len(test_index)}个案例")
            
            # 创建质量筛选器
            logger = LoggerConfig.setup_logger('test_enhanced_exception')
            quality_filter = QualityFilter(logger=logger)
            print("2. 创建质量筛选器成功")
            
            # 执行质量筛选
            print("3. 执行质量筛选...")
            result = quality_filter.filter_samples(test_index, "test")
            
            # 验证结果结构
            assert "filtered_index" in result
            assert "statistics" in result
            assert "exception_report" in result
            print("   ✅ 返回结果结构正确")
            
            # 验证统计信息
            stats = result["statistics"]
            print(f"   - 总处理: {stats['total_processed']}")
            print(f"   - 有效样本: {stats['valid_samples']}")
            print(f"   - 筛选掉: {stats['filtered_samples']}")
            print(f"   - 错误: {stats['error_samples']}")
            
            # 验证异常报告详细信息
            report = result["exception_report"]
            print(f"4. 异常报告详细分析:")
            print(f"   - 成功率: {report['summary']['success_rate']:.1f}%")
            print(f"   - 质量筛选数量: {report['quality_filtered']['count']}")
            print(f"   - 文件缺失 - 孤儿图像: {len(report['file_missing']['orphan_images'])}")
            print(f"   - 文件缺失 - 孤儿标注: {len(report['file_missing']['orphan_annotations'])}")
            print(f"   - 格式错误 - JSON语法错误: {len(report['format_errors']['json_syntax_errors'])}")
            print(f"   - 格式错误 - 缺失字段: {len(report['format_errors']['missing_fields'])}")
            print(f"   - 格式错误 - 类型错误: {len(report['format_errors']['type_errors'])}")
            print(f"   - 文件访问错误: {len(report['file_access_errors'])}")
            
            # 验证具体异常检测
            expected_checks = {
                "orphan_images": 1,  # 应该有1个缺失的图像文件
                "json_syntax_errors": 1,  # 应该有1个JSON语法错误
                "missing_fields": 1,  # 应该有1个缺失字段错误
                "type_errors": 1,  # 应该有1个类型错误
                "quality_filtered": 1  # 应该有1个质量不合格的文件
            }
            
            checks_passed = 0
            total_checks = len(expected_checks)
            
            if len(report['file_missing']['orphan_images']) >= expected_checks["orphan_images"]:
                print("   ✅ 孤儿图像检测正确")
                checks_passed += 1
            else:
                print("   ❌ 孤儿图像检测异常")
            
            if len(report['format_errors']['json_syntax_errors']) >= expected_checks["json_syntax_errors"]:
                print("   ✅ JSON语法错误检测正确")
                checks_passed += 1
            else:
                print("   ❌ JSON语法错误检测异常")
            
            if len(report['format_errors']['missing_fields']) >= expected_checks["missing_fields"]:
                print("   ✅ 缺失字段检测正确")
                checks_passed += 1
            else:
                print("   ❌ 缺失字段检测异常")
            
            if len(report['format_errors']['type_errors']) >= expected_checks["type_errors"]:
                print("   ✅ 类型错误检测正确")
                checks_passed += 1
            else:
                print("   ❌ 类型错误检测异常")
            
            if report['quality_filtered']['count'] >= expected_checks["quality_filtered"]:
                print("   ✅ 质量筛选检测正确")
                checks_passed += 1
            else:
                print("   ❌ 质量筛选检测异常")
            
            print(f"5. 异常检测验证: {checks_passed}/{total_checks} 通过")
            
            return checks_passed == total_checks
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_progress_logging():
    """测试进度日志功能"""
    try:
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        print("=== 测试进度日志功能 ===")
        print()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建大量测试数据以触发进度日志
            test_index = {}
            for i in range(2500):  # 创建2500个文件，应该触发进度日志
                image_path = os.path.join(temp_dir, f"image_{i}.jpg")
                annotation_path = os.path.join(temp_dir, f"annotation_{i}.json")
                
                # 只创建标注文件，图像文件不存在（模拟异常情况）
                with open(annotation_path, 'w', encoding='utf-8') as f:
                    json.dump({"quality": "合格"}, f, ensure_ascii=False)
                
                test_index[i] = {
                    "image_path": image_path,
                    "annotation_path": annotation_path
                }
            
            print(f"1. 创建大量测试数据: {len(test_index)}个文件对")
            
            # 创建质量筛选器
            logger = LoggerConfig.setup_logger('test_progress')
            quality_filter = QualityFilter(logger=logger)
            
            # 执行质量筛选（应该看到进度日志）
            print("2. 执行质量筛选（观察进度日志）...")
            result = quality_filter.filter_samples(test_index, "test")
            
            print("3. ✅ 进度日志功能验证通过")
            return True
            
    except Exception as e:
        print(f"❌ 进度日志测试异常: {e}")
        return False

def test_strict_mode():
    """测试严格模式功能"""
    try:
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        print("=== 测试严格模式功能 ===")
        print()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建缺失quality字段的测试文件
            image_path = os.path.join(temp_dir, "test.jpg")
            annotation_path = os.path.join(temp_dir, "test.json")
            
            with open(image_path, 'w') as f:
                f.write("fake image")
            with open(annotation_path, 'w', encoding='utf-8') as f:
                json.dump({"bbox": {"p1": [0, 0]}}, f, ensure_ascii=False)  # 缺失quality字段
            
            test_index = {1: {"image_path": image_path, "annotation_path": annotation_path}}
            
            # 测试非严格模式
            logger = LoggerConfig.setup_logger('test_strict_mode')
            config_non_strict = {"strict_mode": False, "default_quality": "unknown"}
            filter_non_strict = QualityFilter(config=config_non_strict, logger=logger)
            
            result_non_strict = filter_non_strict.filter_samples(test_index, "test")
            print(f"1. 非严格模式 - 有效样本: {result_non_strict['statistics']['valid_samples']}")
            
            # 测试严格模式
            config_strict = {"strict_mode": True, "accepted_values": ["合格"]}
            filter_strict = QualityFilter(config=config_strict, logger=logger)
            
            result_strict = filter_strict.filter_samples(test_index, "test")
            print(f"2. 严格模式 - 有效样本: {result_strict['statistics']['valid_samples']}")
            
            # 验证严格模式下缺失字段被正确处理
            if result_strict['statistics']['valid_samples'] == 0:
                print("3. ✅ 严格模式功能验证通过")
                return True
            else:
                print("3. ❌ 严格模式功能验证失败")
                return False
            
    except Exception as e:
        print(f"❌ 严格模式测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("增强异常处理和报告机制测试 - 步骤3.3")
    print("=" * 60)
    print()
    
    success_count = 0
    total_tests = 3
    
    # 执行各项测试
    if test_enhanced_exception_handling():
        success_count += 1
    print()
    
    if test_progress_logging():
        success_count += 1
    print()
    
    if test_strict_mode():
        success_count += 1
    print()
    
    # 生成测试报告
    print("=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    print(f"总测试数: {total_tests}")
    print(f"通过: {success_count}")
    print(f"失败: {total_tests - success_count}")
    print(f"成功率: {(success_count / total_tests * 100):.1f}%")
    print()
    
    if success_count == total_tests:
        print("🎉 所有测试通过！增强异常处理功能验证成功！")
        return 0
    else:
        print("❌ 部分测试失败，请检查详细日志")
        return 1

if __name__ == "__main__":
    exit(main())
