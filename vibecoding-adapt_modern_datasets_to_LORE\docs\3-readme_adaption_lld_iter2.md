# LORE-TSR项目TableLabelMe数据格式支持详细设计（迭代2）

## 项目结构与总体设计

### 设计目标
在迭代1基础架构之上，实现完整TableLabelMe数据集的目录扫描和文件索引构建功能。将固定测试数据替换为真实的目录扫描机制，支持part_xxxx目录结构的递归扫描和图像-标注文件的自动匹配。

### 核心设计原则
- **简约至上**：使用标准库os.walk实现目录扫描，避免复杂的并发和缓存机制
- **无侵害性**：保持迭代1的所有公共接口不变，确保完全向后兼容
- **接口一致性**：_build_file_index方法返回格式保持兼容，无需修改下游代码
- **模块化扩展**：新增独立的文件扫描模块，为后续迭代预留清晰演进路径

## 目录结构树 (Directory Tree)

```
LORE-TSR/src/lib/datasets/
├── dataset_factory.py                    # [现有] 数据集工厂，无需修改
├── dataset/
│   ├── table.py                         # [现有] COCO格式标准数据集
│   ├── table_mid.py                     # [现有] COCO格式中等尺寸数据集
│   ├── table_small.py                   # [现有] COCO格式小尺寸数据集
│   └── table_labelmev2.py               # [修改] 替换_build_file_index实现
├── sample/
│   └── ctdet.py                         # [现有] CenterNet采样逻辑，无需修改
└── parsers/                             # [现有] 格式解析器模块
    ├── __init__.py                      # [扩展] 添加FileScanner导入
    ├── base_parser.py                   # [现有] 解析器基类，无需修改
    ├── tablelabelme_parser.py           # [现有] TableLabelMe格式解析器，无需修改
    └── file_scanner.py                  # [新增] 目录扫描和文件索引构建模块

LORE-TSR/src/lib/
├── opts.py                              # [现有] 参数解析，无需修改
└── configs/                             # [现有] 配置文件目录
    └── dataset_configs.py               # [占位] 外部配置文件模板（迭代4实现）
```

## 整体逻辑和交互时序图

### 核心工作流程
迭代2在保持迭代1调用链不变的前提下，将固定测试数据替换为真实的目录扫描和文件索引构建。

```mermaid
sequenceDiagram
    participant Main as main.py
    participant TLD as TableLabelMeDataset
    participant FS as FileScanner
    participant TLP as TableLabelMeParser
    participant CTD as CTDetDataset

    Main->>TLD: 创建数据集实例
    TLD->>TLD: __init__(opt, split)
    TLD->>FS: 创建FileScanner实例
    TLD->>FS: scan_directories(data_paths)
    
    FS->>FS: 递归扫描part_xxxx目录
    FS->>FS: 匹配图像-标注文件对
    FS->>FS: 生成image_id哈希
    FS->>FS: 构建文件索引字典
    FS-->>TLD: 返回完整文件索引

    TLD->>TLD: _load_annotations()
    loop 每个文件对
        TLD->>TLP: parse_file(json_path, image_path)
        TLP-->>TLD: 标准化数据字典
    end

    Main->>TLD: dataset[index]
    TLD->>CTD: __getitem__(标准化数据)
    CTD-->>TLD: 训练样本字典
    TLD-->>Main: 与COCO格式兼容的数据
```

## 数据实体结构深化

### 文件索引数据结构
```python
file_index = {
    image_id: {
        "image_path": "/absolute/path/to/image.jpg",      # 图像文件绝对路径
        "annotation_path": "/absolute/path/to/anno.json", # 标注文件绝对路径
        "part_dir": "part_0001",                          # 所属part目录名
        "dataset_source": "TabRecSet_chinese",            # 数据源标识
        "relative_path": "part_0001/image.jpg"            # 相对路径（调试用）
    }
}
```

### 扫描统计信息结构
```python
scan_statistics = {
    "total_directories": 156,          # 扫描的总目录数
    "part_directories": 12,            # 有效的part目录数
    "total_images": 8543,              # 发现的图像文件总数
    "total_annotations": 8543,         # 发现的标注文件总数
    "valid_pairs": 8543,               # 有效的图像-标注对数
    "orphan_images": 0,                # 无对应标注的图像数
    "orphan_annotations": 0,           # 无对应图像的标注数
    "duplicate_ids": 0,                # ID冲突数量
    "scan_time": 12.34                 # 扫描耗时（秒）
}
```

### 数据实体关系图
```mermaid
erDiagram
    DatasetPath {
        string root_path
        string dataset_source
        string split_type
    }
    
    PartDirectory {
        string part_name
        string full_path
        int file_count
    }
    
    FileIndex {
        int image_id
        string image_path
        string annotation_path
        string part_dir
        string dataset_source
    }
    
    ScanStatistics {
        int total_directories
        int part_directories
        int valid_pairs
        float scan_time
    }
    
    DatasetPath ||--o{ PartDirectory : "包含"
    PartDirectory ||--o{ FileIndex : "生成"
    FileIndex }o--|| ScanStatistics : "统计"
```

## 配置项

### 扫描配置参数
```python
scan_config = {
    "part_pattern": r"part_\d{4}",                    # part目录匹配模式
    "image_extensions": [".jpg", ".jpeg", ".png"],    # 支持的图像格式
    "annotation_patterns": [".json", "_table_annotation.json"],  # 标注文件模式
    "max_depth": 3,                                   # 最大扫描深度
    "follow_symlinks": False,                         # 是否跟随符号链接
    "case_sensitive": False                           # 文件名匹配是否区分大小写
}
```

### 数据路径配置（迭代2简化版本）
```python
# 当前迭代使用硬编码路径，迭代4将支持外部配置文件
data_paths = {
    'train': [
        '/path/to/TabRecSet_chinese/train',
        '/path/to/TabRecSet_english/train'
    ],
    'val': [
        '/path/to/TabRecSet_chinese/val',
        '/path/to/TabRecSet_english/val'
    ]
}
```

## 模块化文件详解 (File-by-File Breakdown)

### src/lib/datasets/parsers/__init__.py
**a. 文件用途说明**
扩展解析器包的初始化文件，添加FileScanner类的导入支持。

**b. 修改内容**
```python
from .base_parser import BaseParser
from .tablelabelme_parser import TableLabelMeParser
from .file_scanner import FileScanner  # 新增导入

__all__ = ['BaseParser', 'TableLabelMeParser', 'FileScanner']
```

### src/lib/datasets/parsers/file_scanner.py
**a. 文件用途说明**
专门负责TableLabelMe数据集的目录扫描、文件匹配和索引构建的核心模块。实现递归目录遍历、图像-标注文件对匹配、唯一ID生成和统计信息收集。

**b. 文件内类图**
```mermaid
classDiagram
    class FileScanner {
        +__init__(config)
        +scan_directories(data_paths, split) dict
        +_scan_single_directory(root_path, dataset_source) dict
        +_find_part_directories(root_path) list
        +_scan_part_directory(part_path) dict
        +_match_image_annotation_pairs(files) list
        +_generate_image_id(file_path) int
        +_normalize_path(path) str
        +_collect_statistics(scan_results) dict
        +_validate_file_pair(image_path, annotation_path) bool
    }
```

**c. 函数/方法详解**

#### scan_directories方法
- **用途**: 扫描多个数据源目录，构建完整的文件索引
- **输入参数**:
  - `data_paths`: 数据路径列表，如['/path/to/dataset1', '/path/to/dataset2']
  - `split`: 数据集分割类型，如'train'或'val'
- **输出数据结构**: dict - 完整的文件索引字典和统计信息
- **实现流程**:
```mermaid
flowchart TD
    A[接收数据路径列表] --> B[初始化结果字典]
    B --> C{遍历每个数据路径}
    C --> D[扫描单个目录]
    D --> E[合并扫描结果]
    E --> F{还有路径?}
    F -->|是| C
    F -->|否| G[收集统计信息]
    G --> H[验证ID唯一性]
    H --> I[返回完整索引]
```

#### _scan_single_directory方法
- **用途**: 扫描单个数据源目录，识别part子目录并构建文件映射
- **输入参数**:
  - `root_path`: 数据源根目录路径
  - `dataset_source`: 数据源标识名称
- **输出数据结构**: dict - 该数据源的文件索引字典
- **实现流程**:
```mermaid
sequenceDiagram
    participant Scanner as FileScanner
    participant FS as 文件系统
    participant Matcher as 文件匹配器

    Scanner->>FS: 查找part目录
    FS-->>Scanner: part目录列表

    loop 每个part目录
        Scanner->>FS: 扫描目录文件
        FS-->>Scanner: 文件列表
        Scanner->>Matcher: 匹配图像-标注对
        Matcher-->>Scanner: 文件对列表
        Scanner->>Scanner: 生成image_id
    end

    Scanner-->>Scanner: 返回索引字典
```

#### _find_part_directories方法
- **用途**: 在根目录下查找符合part_xxxx模式的子目录
- **输入参数**: `root_path` - 根目录路径
- **输出数据结构**: list - part目录路径列表
- **实现流程**: 使用正则表达式匹配目录名，过滤出符合模式的目录

#### _match_image_annotation_pairs方法
- **用途**: 在part目录中匹配图像文件和对应的标注文件
- **输入参数**: `files` - 目录中的文件列表
- **输出数据结构**: list - 匹配成功的文件对列表
- **实现流程**:
```mermaid
flowchart TD
    A[获取文件列表] --> B[分离图像和标注文件]
    B --> C[按文件名前缀分组]
    C --> D{遍历图像文件}
    D --> E[查找对应标注文件]
    E --> F{找到匹配?}
    F -->|是| G[添加到结果列表]
    F -->|否| H[记录孤儿文件]
    G --> I{还有图像?}
    H --> I
    I -->|是| D
    I -->|否| J[返回匹配对列表]
```

#### _generate_image_id方法
- **用途**: 基于文件路径生成稳定唯一的图像ID
- **输入参数**: `file_path` - 图像文件路径
- **输出数据结构**: int - 64位整数ID
- **实现流程**: 使用MD5哈希算法处理规范化路径，转换为整数ID

### src/lib/datasets/dataset/table_labelmev2.py
**a. 文件用途说明**
修改TableLabelMe数据集类，将固定测试数据替换为真实的目录扫描机制。

**b. 主要修改内容**

#### _build_file_index方法（重写）
- **用途**: 构建真实的文件索引，替换迭代1的固定测试数据
- **输入参数**: 无
- **输出数据结构**: dict - 文件映射字典
- **实现流程**:
```mermaid
sequenceDiagram
    participant Dataset as TableLabelMe
    participant Scanner as FileScanner
    participant Config as 配置管理

    Dataset->>Config: 获取数据路径配置
    Config-->>Dataset: 数据路径列表
    Dataset->>Scanner: 创建扫描器实例
    Dataset->>Scanner: scan_directories(paths, split)
    Scanner->>Scanner: 执行目录扫描
    Scanner-->>Dataset: 文件索引和统计信息
    Dataset->>Dataset: 保存索引和统计信息
    Dataset-->>Dataset: 返回文件索引
```

#### __init__方法（扩展）
- **用途**: 初始化时集成FileScanner，设置扫描配置
- **输入参数**:
  - `opt`: 配置对象
  - `split`: 数据集分割（train/val/test）
- **输出数据结构**: 无（构造函数）
- **实现流程**: 在原有初始化基础上，添加FileScanner的创建和配置

## 迭代演进依据

### 架构扩展性设计
1. **模块化扩展**: FileScanner作为独立模块，可支持未来其他数据格式的目录扫描需求
2. **配置系统预留**: 扫描配置参数为迭代4的外部配置文件系统预留接口
3. **性能优化预留**: 扫描结果结构为迭代5的缓存机制预留扩展点
4. **质量控制预留**: 文件验证接口为迭代3的质量筛选功能预留扩展

### 后续迭代占位
- **迭代3**: 质量筛选功能通过`_validate_file_pair`方法预留接口
- **迭代4**: 配置系统集成通过`scan_config`参数结构预留
- **迭代5**: 训练流程集成通过现有数据集接口无缝支持
- **迭代6**: 可视化工具可直接使用FileScanner进行数据验证

### 技术债务控制
- FileScanner模块控制在400行以内，功能完整且独立
- 保持与迭代1完全兼容，无破坏性变更
- 使用标准库实现，避免引入额外依赖
- 清晰的错误处理和日志记录机制

## 如何迁移现有固定数据功能

### 代码文件对应关系
| 迭代1固定实现 | 迭代2真实实现 | 迁移策略 |
|-------------|-------------|---------|
| `_build_file_index`固定返回 | `FileScanner.scan_directories` | 替换实现，保持接口兼容 |
| 硬编码测试数据 | 真实目录扫描 | 动态生成，支持多源数据 |
| 固定image_id | 哈希生成image_id | 算法生成，确保唯一性 |
| 简单统计信息 | 详细扫描统计 | 扩展信息，便于调试分析 |

### 兼容性保证
- 所有公共接口保持不变，确保下游代码无需修改
- 数据结构格式完全兼容，保证与解析器的无缝集成
- 错误处理机制向后兼容，不影响现有异常处理逻辑
- 性能特征保持一致，不显著影响数据加载速度

### 验证策略
- 使用相同的测试数据验证迭代1和迭代2的输出一致性
- 确保生成的image_id具有稳定性和唯一性
- 验证多源数据合并功能的正确性
- 测试大规模数据集的扫描性能和内存使用

---

**文档版本**: v2.0
**创建日期**: 2025年1月21日
**迭代范围**: 迭代2 - 数据集扫描和索引构建
**依赖迭代**: 基于迭代1的基础架构实现
**后续迭代**: 为迭代3-6预留清晰的演进接口
