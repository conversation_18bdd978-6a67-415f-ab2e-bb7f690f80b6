#!/usr/bin/env python3
"""
验证脚本 - 步骤3.2：质量筛选核心模块测试
测试QualityFilter模块的基本功能
"""

import sys
import os
import json
import tempfile

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_data(temp_dir: str):
    """创建测试数据"""
    test_files = []
    
    # 创建正常的合格文件
    image1 = os.path.join(temp_dir, "image1.jpg")
    annotation1 = os.path.join(temp_dir, "annotation1.json")
    with open(image1, 'w') as f:
        f.write("fake image data")
    with open(annotation1, 'w', encoding='utf-8') as f:
        json.dump({
            "quality": "合格",
            "bbox": {"p1": [0, 0], "p2": [100, 0], "p3": [100, 50], "p4": [0, 50]},
            "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}
        }, f, ensure_ascii=False)
    test_files.append((1, {"image_path": image1, "annotation_path": annotation1}))
    
    # 创建不合格文件
    image2 = os.path.join(temp_dir, "image2.jpg")
    annotation2 = os.path.join(temp_dir, "annotation2.json")
    with open(image2, 'w') as f:
        f.write("fake image data")
    with open(annotation2, 'w', encoding='utf-8') as f:
        json.dump({
            "quality": "不合格",
            "bbox": {"p1": [0, 0], "p2": [100, 0], "p3": [100, 50], "p4": [0, 50]}
        }, f, ensure_ascii=False)
    test_files.append((2, {"image_path": image2, "annotation_path": annotation2}))
    
    # 创建缺失quality字段的文件
    image3 = os.path.join(temp_dir, "image3.jpg")
    annotation3 = os.path.join(temp_dir, "annotation3.json")
    with open(image3, 'w') as f:
        f.write("fake image data")
    with open(annotation3, 'w', encoding='utf-8') as f:
        json.dump({
            "bbox": {"p1": [0, 0], "p2": [100, 0], "p3": [100, 50], "p4": [0, 50]}
        }, f, ensure_ascii=False)
    test_files.append((3, {"image_path": image3, "annotation_path": annotation3}))
    
    # 创建qualified英文标记的文件
    image4 = os.path.join(temp_dir, "image4.jpg")
    annotation4 = os.path.join(temp_dir, "annotation4.json")
    with open(image4, 'w') as f:
        f.write("fake image data")
    with open(annotation4, 'w', encoding='utf-8') as f:
        json.dump({
            "quality": "qualified",
            "bbox": {"p1": [0, 0], "p2": [100, 0], "p3": [100, 50], "p4": [0, 50]}
        }, f, ensure_ascii=False)
    test_files.append((4, {"image_path": image4, "annotation_path": annotation4}))
    
    return dict(test_files)

def test_quality_filter_basic():
    """测试质量筛选基础功能"""
    try:
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        print("=== 测试质量筛选核心模块 ===")
        print()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试数据
            test_index = create_test_data(temp_dir)
            print(f"1. 创建测试数据: {len(test_index)}个文件对")
            
            # 创建质量筛选器
            logger = LoggerConfig.setup_logger('test_quality_filter')
            quality_filter = QualityFilter(logger=logger)
            print("2. 创建质量筛选器成功")
            
            # 执行质量筛选
            print("3. 执行质量筛选...")
            result = quality_filter.filter_samples(test_index, "test")
            
            # 验证结果结构
            assert "filtered_index" in result
            assert "statistics" in result
            assert "exception_report" in result
            print("   ✅ 返回结果结构正确")
            
            # 验证统计信息
            stats = result["statistics"]
            print(f"   - 总处理: {stats['total_processed']}")
            print(f"   - 有效样本: {stats['valid_samples']}")
            print(f"   - 筛选掉: {stats['filtered_samples']}")
            print(f"   - 错误: {stats['error_samples']}")
            
            assert stats["total_processed"] == len(test_index)
            assert stats["valid_samples"] + stats["filtered_samples"] + stats["error_samples"] == stats["total_processed"]
            print("   ✅ 统计信息验证通过")
            
            # 验证筛选结果
            filtered_index = result["filtered_index"]
            print(f"   - 筛选后有效文件: {len(filtered_index)}")
            
            # 应该有2个合格文件（"合格"和"qualified"）
            expected_valid = 2
            if stats["valid_samples"] == expected_valid:
                print("   ✅ 质量筛选逻辑正确")
            else:
                print(f"   ⚠️ 预期有效样本{expected_valid}个，实际{stats['valid_samples']}个")
            
            # 验证异常报告
            report = result["exception_report"]
            print(f"   - 成功率: {report['summary']['success_rate']:.1f}%")
            print(f"   - 质量筛选数量: {report['quality_filtered']['count']}")
            
            print("4. ✅ 质量筛选基础功能验证通过")
            
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_quality_filter_config():
    """测试质量筛选配置功能"""
    try:
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        print("=== 测试质量筛选配置功能 ===")
        print()
        
        # 测试默认配置
        print("1. 测试默认配置...")
        logger = LoggerConfig.setup_logger('test_config')
        filter1 = QualityFilter(logger=logger)
        default_config = filter1.config
        print(f"   默认配置: {default_config}")
        print("   ✅ 默认配置获取成功")
        
        # 测试自定义配置
        print("2. 测试自定义配置...")
        custom_config = {
            "enabled": True,
            "accepted_values": ["good", "excellent"],
            "case_sensitive": True,
            "default_quality": "unknown",
            "strict_mode": True
        }
        filter2 = QualityFilter(config=custom_config, logger=logger)
        assert filter2.config["accepted_values"] == ["good", "excellent"]
        assert filter2.config["case_sensitive"] == True
        print("   ✅ 自定义配置设置成功")
        
        print("3. ✅ 质量筛选配置功能验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试异常: {e}")
        return False

def test_quality_filter_exception_handling():
    """测试异常处理功能"""
    try:
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        print("=== 测试异常处理功能 ===")
        print()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建包含异常的测试数据
            test_cases = {}
            
            # 正常文件
            image1 = os.path.join(temp_dir, "normal.jpg")
            annotation1 = os.path.join(temp_dir, "normal.json")
            with open(image1, 'w') as f:
                f.write("normal image")
            with open(annotation1, 'w', encoding='utf-8') as f:
                json.dump({"quality": "合格"}, f, ensure_ascii=False)
            test_cases[1] = {"image_path": image1, "annotation_path": annotation1}
            
            # 缺失图像文件
            missing_image = os.path.join(temp_dir, "missing.jpg")
            annotation2 = os.path.join(temp_dir, "annotation2.json")
            with open(annotation2, 'w', encoding='utf-8') as f:
                json.dump({"quality": "合格"}, f, ensure_ascii=False)
            test_cases[2] = {"image_path": missing_image, "annotation_path": annotation2}
            
            # JSON格式错误
            image3 = os.path.join(temp_dir, "bad_json.jpg")
            annotation3 = os.path.join(temp_dir, "bad_json.json")
            with open(image3, 'w') as f:
                f.write("image")
            with open(annotation3, 'w') as f:
                f.write("invalid json content {")
            test_cases[3] = {"image_path": image3, "annotation_path": annotation3}
            
            print(f"1. 创建异常测试数据: {len(test_cases)}个案例")
            
            # 执行质量筛选
            logger = LoggerConfig.setup_logger('test_exception')
            quality_filter = QualityFilter(logger=logger)
            result = quality_filter.filter_samples(test_cases, "test")
            
            # 验证异常处理
            report = result["exception_report"]
            print(f"2. 异常处理结果:")
            print(f"   - 文件缺失: {len(report['file_missing']['orphan_images'])}个")
            print(f"   - JSON错误: {len(report['format_errors']['json_syntax_errors'])}个")
            print(f"   - 访问错误: {len(report['file_access_errors'])}个")
            
            # 应该有缺失的图像文件和JSON错误
            has_missing_files = len(report["file_missing"]["orphan_images"]) > 0
            has_json_errors = len(report["format_errors"]["json_syntax_errors"]) > 0
            
            if has_missing_files and has_json_errors:
                print("   ✅ 异常检测正确")
            else:
                print("   ⚠️ 异常检测可能有问题")
            
            print("3. ✅ 异常处理功能验证通过")
            
        return True
        
    except Exception as e:
        print(f"❌ 异常处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("质量筛选核心模块测试 - 步骤3.2")
    print("=" * 50)
    print()
    
    success_count = 0
    total_tests = 3
    
    # 执行各项测试
    if test_quality_filter_basic():
        success_count += 1
    print()
    
    if test_quality_filter_config():
        success_count += 1
    print()
    
    if test_quality_filter_exception_handling():
        success_count += 1
    print()
    
    # 生成测试报告
    print("=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    print(f"总测试数: {total_tests}")
    print(f"通过: {success_count}")
    print(f"失败: {total_tests - success_count}")
    print(f"成功率: {(success_count / total_tests * 100):.1f}%")
    print()
    
    if success_count == total_tests:
        print("🎉 所有测试通过！质量筛选核心模块验证成功！")
        return 0
    else:
        print("❌ 部分测试失败，请检查详细日志")
        return 1

if __name__ == "__main__":
    exit(main())
