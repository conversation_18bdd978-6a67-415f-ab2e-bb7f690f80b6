# src/lib/datasets/parsers/quality_filter.py
"""
质量筛选模块 - TableLabelMe数据格式支持项目
实现基于quality字段的数据筛选、异常处理和统计报告功能
"""

import json
import os
from typing import Dict, List, Tuple, Optional, Any
import logging


class QualityFilter:
    """TableLabelMe数据集质量筛选器"""
    
    def __init__(self, config: Optional[Dict] = None, logger: Optional[logging.Logger] = None):
        """
        初始化质量筛选器

        Args:
            config (Optional[Dict]): 质量筛选配置字典，如果为None则使用默认配置
            logger (Optional[logging.Logger]): 日志记录器，如果为None则创建默认记录器
        """
        # 合并默认配置和自定义配置
        self.config = self._get_default_config()
        if config:
            self.config.update(config)
        self.logger = logger or logging.getLogger(__name__)
        
        # 统计信息
        self.statistics = {
            "total_processed": 0,
            "valid_samples": 0,
            "filtered_samples": 0,
            "error_samples": 0
        }
        
        # 异常记录
        self.quality_filtered = []
        self.file_missing = {"orphan_images": [], "orphan_annotations": []}
        self.format_errors = {
            "json_syntax_errors": [],
            "missing_fields": [],
            "type_errors": []
        }
        self.file_access_errors = []
    
    def _get_default_config(self) -> Dict:
        """
        获取默认质量筛选配置

        Returns:
            Dict: 默认配置字典
        """
        return {
            "enabled": True,
            "accepted_values": ["合格", "qualified", "good"],
            "case_sensitive": False,
            "default_quality": "unknown",
            "strict_mode": False,
            "quality_field_path": "quality",
            "max_errors_per_part": 100,
            "error_sampling_rate": 0.1
        }
    
    def filter_samples(self, file_index: Dict, split: str) -> Dict:
        """
        对文件索引进行质量筛选

        Args:
            file_index (Dict): 来自FileScanner的完整文件索引
            split (str): 数据集分割类型（train/val/test）

        Returns:
            Dict: 包含筛选后索引和异常报告的字典
        """
        total_files = len(file_index)
        self.logger.info(f"开始质量筛选 - {split}数据集，总文件数: {total_files}")

        filtered_index = {}

        for idx, (image_id, file_info) in enumerate(file_index.items(), 1):
            self.statistics["total_processed"] += 1

            # 记录处理进度
            self._log_progress(idx, total_files)

            try:
                # 验证文件对有效性
                if not self._validate_file_pair(file_info["image_path"], file_info["annotation_path"]):
                    continue

                # 检查质量字段
                is_valid, quality_value, reason = self._check_quality_from_file(file_info["annotation_path"])

                if is_valid:
                    filtered_index[image_id] = file_info
                    self.statistics["valid_samples"] += 1
                else:
                    self.quality_filtered.append({
                        "path": file_info["annotation_path"],
                        "quality": quality_value,
                        "reason": reason
                    })
                    self.statistics["filtered_samples"] += 1
                    self.logger.warning(f"质量筛选跳过: {file_info['image_path']} (质量: {quality_value})")

            except Exception as e:
                self._handle_exception(e, {
                    "image_path": file_info["image_path"],
                    "annotation_path": file_info["annotation_path"],
                    "operation": "quality_filtering"
                })
                self.statistics["error_samples"] += 1

        self.logger.info(f"质量筛选完成 - 有效样本: {self.statistics['valid_samples']}, "
                        f"筛选掉: {self.statistics['filtered_samples']}, "
                        f"错误: {self.statistics['error_samples']}")

        return {
            "filtered_index": filtered_index,
            "statistics": self.statistics.copy(),
            "exception_report": self.generate_report()
        }
    
    def _validate_file_pair(self, image_path: str, annotation_path: str) -> bool:
        """
        验证图像文件和标注文件对的有效性
        
        Args:
            image_path (str): 图像文件路径
            annotation_path (str): 标注文件路径
            
        Returns:
            bool: 文件对是否有效
        """
        # 检查图像文件
        if not os.path.exists(image_path):
            self.file_missing["orphan_images"].append({
                "path": image_path,
                "reason": "图像文件不存在"
            })
            return False

        # 检查标注文件
        if not os.path.exists(annotation_path):
            self.file_missing["orphan_annotations"].append({
                "path": annotation_path,
                "reason": "标注文件不存在"
            })
            return False

        # 检查文件可读性
        try:
            with open(image_path, 'rb') as f:
                f.read(1)
            with open(annotation_path, 'r', encoding='utf-8') as f:
                f.read(1)
        except PermissionError as e:
            self.file_access_errors.append({
                "path": f"{image_path}, {annotation_path}",
                "error": f"权限不足: {str(e)}"
            })
            return False
        except Exception as e:
            self.file_access_errors.append({
                "path": f"{image_path}, {annotation_path}",
                "error": f"文件访问错误: {str(e)}"
            })
            return False

        return True
    
    def _check_quality_from_file(self, annotation_path: str) -> Tuple[bool, str, str]:
        """
        从文件中检查质量字段

        Args:
            annotation_path (str): 标注文件路径

        Returns:
            Tuple[bool, str, str]: (是否合格, 质量值, 原因)
        """
        try:
            with open(annotation_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 设置当前处理的文件路径，用于错误记录
            self._current_annotation_path = annotation_path
            result = self._check_quality(data)
            self._current_annotation_path = None
            return result

        except json.JSONDecodeError as e:
            self.format_errors["json_syntax_errors"].append({
                "path": annotation_path,
                "error": f"JSON语法错误: {str(e)}",
                "line": getattr(e, 'lineno', -1)
            })
            return False, "json_error", "JSON格式错误"

        except Exception as e:
            self.file_access_errors.append({
                "path": annotation_path,
                "error": f"文件读取错误: {str(e)}"
            })
            return False, "file_error", "文件读取失败"
    
    def _check_quality(self, annotation_data: Dict) -> Tuple[bool, str, str]:
        """
        检查标注数据的质量字段是否符合要求

        Args:
            annotation_data (Dict): 解析后的标注数据

        Returns:
            Tuple[bool, str, str]: (是否合格, 质量值, 原因)
        """
        quality_field = self.config["quality_field_path"]

        # 检查quality字段是否存在
        if quality_field not in annotation_data:
            # 记录缺失字段错误
            current_path = getattr(self, '_current_annotation_path', 'unknown')
            self.format_errors["missing_fields"].append({
                "path": current_path,
                "missing_fields": [quality_field]
            })

            if self.config["strict_mode"]:
                return False, "missing", "质量字段缺失且为严格模式"
            else:
                quality_value = self.config["default_quality"]
        else:
            quality_value = annotation_data[quality_field]

        # 检查质量值类型
        if not isinstance(quality_value, str):
            current_path = getattr(self, '_current_annotation_path', 'unknown')
            self.format_errors["type_errors"].append({
                "path": current_path,
                "field": quality_field,
                "expected": "str",
                "actual": type(quality_value).__name__
            })
            return False, str(quality_value), "质量字段类型错误"

        # 检查质量值是否可接受
        if self._is_quality_acceptable(quality_value):
            return True, quality_value, "质量合格"
        else:
            return False, quality_value, "质量不符合要求"
    
    def _is_quality_acceptable(self, quality_value: str) -> bool:
        """
        检查质量值是否在接受列表中
        
        Args:
            quality_value (str): 质量值
            
        Returns:
            bool: 质量值是否可接受
        """
        accepted_values = self.config["accepted_values"]

        if self.config["case_sensitive"]:
            return quality_value in accepted_values
        else:
            return quality_value.lower() in [v.lower() for v in accepted_values]
    
    def _handle_exception(self, exception: Exception, context: Dict) -> None:
        """
        统一处理各种异常情况
        
        Args:
            exception (Exception): 异常对象
            context (Dict): 异常上下文信息（文件路径、操作类型等）
        """
        error_info = {
            "path": context.get("annotation_path", "unknown"),
            "error": f"{type(exception).__name__}: {str(exception)}",
            "operation": context.get("operation", "unknown")
        }

        self.file_access_errors.append(error_info)
        self.logger.error(f"异常处理: {error_info}")
    
    def generate_report(self) -> Dict:
        """
        生成详细的异常统计报告
        
        Returns:
            Dict: 完整的异常报告字典
        """
        return {
            "summary": {
                "total_processed": self.statistics["total_processed"],
                "valid_samples": self.statistics["valid_samples"],
                "filtered_samples": self.statistics["filtered_samples"],
                "error_samples": self.statistics["error_samples"],
                "success_rate": (self.statistics["valid_samples"] / max(self.statistics["total_processed"], 1)) * 100
            },
            "quality_filtered": {
                "count": len(self.quality_filtered),
                "samples": self.quality_filtered
            },
            "file_missing": self.file_missing,
            "format_errors": self.format_errors,
            "file_access_errors": self.file_access_errors
        }
    
    def _log_progress(self, current: int, total: int) -> None:
        """
        记录处理进度
        
        Args:
            current (int): 当前处理数量
            total (int): 总数量
        """
        if current % 1000 == 0 or current == total:
            progress = (current / total) * 100
            self.logger.info(f"质量筛选进度: {current}/{total} ({progress:.1f}%)")
