#!/usr/bin/env python3
"""
验证脚本 - 步骤3.3：基于真实TableLabelMe格式的质量筛选测试
使用真实的TableLabelMe标注格式进行测试
"""

import sys
import os
import json
import tempfile

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_real_format_test_data(temp_dir: str):
    """创建基于真实TableLabelMe格式的测试数据"""
    test_files = []
    
    # 基础的TableLabelMe格式模板
    base_template = {
        "table_ind": 0,
        "image_path": "",
        "type": 1,
        "cells": [
            {
                "cell_ind": 0,
                "header": True,
                "content": [
                    {
                        "bbox": None,
                        "direction": None,
                        "text": "",
                        "score": None
                    }
                ],
                "bbox": {
                    "p1": [185.0, 167.0],
                    "p2": [214.0, 167.0],
                    "p3": [214.0, 188.0],
                    "p4": [185.0, 188.0]
                },
                "lloc": {
                    "start_row": 0,
                    "end_row": 0,
                    "start_col": 0,
                    "end_col": 0
                },
                "border": {
                    "style": {
                        "top": 1,
                        "right": 1,
                        "bottom": 1,
                        "left": 1
                    },
                    "width": None,
                    "color": None
                }
            }
        ]
    }
    
    # 1. 正常的合格文件
    image1 = os.path.join(temp_dir, "qualified.jpg")
    annotation1 = os.path.join(temp_dir, "qualified.json")
    with open(image1, 'w') as f:
        f.write("fake image data")
    
    qualified_data = base_template.copy()
    qualified_data["quality"] = "合格"
    with open(annotation1, 'w', encoding='utf-8') as f:
        json.dump(qualified_data, f, ensure_ascii=False, indent=2)
    test_files.append((1, {"image_path": image1, "annotation_path": annotation1}))
    
    # 2. 英文qualified标记的文件
    image2 = os.path.join(temp_dir, "qualified_en.jpg")
    annotation2 = os.path.join(temp_dir, "qualified_en.json")
    with open(image2, 'w') as f:
        f.write("fake image data")
    
    qualified_en_data = base_template.copy()
    qualified_en_data["quality"] = "qualified"
    with open(annotation2, 'w', encoding='utf-8') as f:
        json.dump(qualified_en_data, f, ensure_ascii=False, indent=2)
    test_files.append((2, {"image_path": image2, "annotation_path": annotation2}))
    
    # 3. good标记的文件
    image3 = os.path.join(temp_dir, "good.jpg")
    annotation3 = os.path.join(temp_dir, "good.json")
    with open(image3, 'w') as f:
        f.write("fake image data")
    
    good_data = base_template.copy()
    good_data["quality"] = "good"
    with open(annotation3, 'w', encoding='utf-8') as f:
        json.dump(good_data, f, ensure_ascii=False, indent=2)
    test_files.append((3, {"image_path": image3, "annotation_path": annotation3}))
    
    # 4. 不合格文件
    image4 = os.path.join(temp_dir, "unqualified.jpg")
    annotation4 = os.path.join(temp_dir, "unqualified.json")
    with open(image4, 'w') as f:
        f.write("fake image data")
    
    unqualified_data = base_template.copy()
    unqualified_data["quality"] = "不合格"
    with open(annotation4, 'w', encoding='utf-8') as f:
        json.dump(unqualified_data, f, ensure_ascii=False, indent=2)
    test_files.append((4, {"image_path": image4, "annotation_path": annotation4}))
    
    # 5. 缺失quality字段的文件
    image5 = os.path.join(temp_dir, "missing_quality.jpg")
    annotation5 = os.path.join(temp_dir, "missing_quality.json")
    with open(image5, 'w') as f:
        f.write("fake image data")
    
    missing_quality_data = base_template.copy()
    # 故意不添加quality字段
    with open(annotation5, 'w', encoding='utf-8') as f:
        json.dump(missing_quality_data, f, ensure_ascii=False, indent=2)
    test_files.append((5, {"image_path": image5, "annotation_path": annotation5}))
    
    # 6. quality字段类型错误的文件
    image6 = os.path.join(temp_dir, "wrong_type.jpg")
    annotation6 = os.path.join(temp_dir, "wrong_type.json")
    with open(image6, 'w') as f:
        f.write("fake image data")
    
    wrong_type_data = base_template.copy()
    wrong_type_data["quality"] = 123  # 应该是字符串，但这里是数字
    with open(annotation6, 'w', encoding='utf-8') as f:
        json.dump(wrong_type_data, f, ensure_ascii=False, indent=2)
    test_files.append((6, {"image_path": image6, "annotation_path": annotation6}))
    
    # 7. 缺失图像文件
    missing_image = os.path.join(temp_dir, "missing_image.jpg")
    annotation7 = os.path.join(temp_dir, "annotation7.json")
    
    missing_img_data = base_template.copy()
    missing_img_data["quality"] = "合格"
    with open(annotation7, 'w', encoding='utf-8') as f:
        json.dump(missing_img_data, f, ensure_ascii=False, indent=2)
    test_files.append((7, {"image_path": missing_image, "annotation_path": annotation7}))
    
    # 8. JSON格式错误的文件
    image8 = os.path.join(temp_dir, "bad_json.jpg")
    annotation8 = os.path.join(temp_dir, "bad_json.json")
    with open(image8, 'w') as f:
        f.write("fake image data")
    
    # 创建明确的JSON语法错误
    bad_json_content = '{"table_ind": 0, "quality": "合格", "cells": ['
    with open(annotation8, 'w', encoding='utf-8') as f:
        f.write(bad_json_content)
    test_files.append((8, {"image_path": image8, "annotation_path": annotation8}))
    
    return dict(test_files)

def test_real_format_quality_filtering():
    """测试基于真实TableLabelMe格式的质量筛选"""
    try:
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        print("=== 测试真实TableLabelMe格式质量筛选 ===")
        print()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建真实格式测试数据
            test_index = create_real_format_test_data(temp_dir)
            print(f"1. 创建真实格式测试数据: {len(test_index)}个案例")
            
            # 创建质量筛选器
            logger = LoggerConfig.setup_logger('test_real_format')
            quality_filter = QualityFilter(logger=logger)
            print("2. 创建质量筛选器成功")
            print(f"   默认配置: {quality_filter.config}")
            
            # 执行质量筛选
            print("3. 执行质量筛选...")
            result = quality_filter.filter_samples(test_index, "test")
            
            # 验证结果结构
            assert "filtered_index" in result
            assert "statistics" in result
            assert "exception_report" in result
            print("   ✅ 返回结果结构正确")
            
            # 验证统计信息
            stats = result["statistics"]
            print(f"4. 统计信息:")
            print(f"   - 总处理: {stats['total_processed']}")
            print(f"   - 有效样本: {stats['valid_samples']}")
            print(f"   - 筛选掉: {stats['filtered_samples']}")
            print(f"   - 错误: {stats['error_samples']}")
            
            # 验证异常报告详细信息
            report = result["exception_report"]
            print(f"5. 异常报告详细分析:")
            print(f"   - 成功率: {report['summary']['success_rate']:.1f}%")
            print(f"   - 质量筛选数量: {report['quality_filtered']['count']}")
            print(f"   - 文件缺失 - 孤儿图像: {len(report['file_missing']['orphan_images'])}")
            print(f"   - 文件缺失 - 孤儿标注: {len(report['file_missing']['orphan_annotations'])}")
            print(f"   - 格式错误 - JSON语法错误: {len(report['format_errors']['json_syntax_errors'])}")
            print(f"   - 格式错误 - 缺失字段: {len(report['format_errors']['missing_fields'])}")
            print(f"   - 格式错误 - 类型错误: {len(report['format_errors']['type_errors'])}")
            print(f"   - 文件访问错误: {len(report['file_access_errors'])}")
            
            # 验证具体的质量筛选结果
            print(f"6. 质量筛选详细结果:")
            for item in report['quality_filtered']['samples']:
                print(f"   - 筛选: {os.path.basename(item['path'])} (质量: {item['quality']}, 原因: {item['reason']})")
            
            # 验证预期结果
            # 应该有3个合格文件（"合格"、"qualified"、"good"）
            expected_valid = 3
            expected_filtered = 4  # "不合格" + 缺失字段 + 类型错误 + JSON错误
            expected_missing_fields = 1  # 缺失quality字段
            expected_type_errors = 1  # 类型错误
            expected_orphan_images = 1  # 缺失图像文件
            expected_json_errors = 1  # JSON语法错误
            
            checks_passed = 0
            total_checks = 6
            
            if stats['valid_samples'] == expected_valid:
                print("   ✅ 有效样本数量正确")
                checks_passed += 1
            else:
                print(f"   ❌ 有效样本数量异常: 预期{expected_valid}, 实际{stats['valid_samples']}")
            
            if report['quality_filtered']['count'] == expected_filtered:
                print("   ✅ 质量筛选数量正确")
                checks_passed += 1
            else:
                print(f"   ❌ 质量筛选数量异常: 预期{expected_filtered}, 实际{report['quality_filtered']['count']}")
            
            if len(report['format_errors']['missing_fields']) == expected_missing_fields:
                print("   ✅ 缺失字段检测正确")
                checks_passed += 1
            else:
                print(f"   ❌ 缺失字段检测异常: 预期{expected_missing_fields}, 实际{len(report['format_errors']['missing_fields'])}")
            
            if len(report['format_errors']['type_errors']) == expected_type_errors:
                print("   ✅ 类型错误检测正确")
                checks_passed += 1
            else:
                print(f"   ❌ 类型错误检测异常: 预期{expected_type_errors}, 实际{len(report['format_errors']['type_errors'])}")
            
            if len(report['file_missing']['orphan_images']) == expected_orphan_images:
                print("   ✅ 孤儿图像检测正确")
                checks_passed += 1
            else:
                print(f"   ❌ 孤儿图像检测异常: 预期{expected_orphan_images}, 实际{len(report['file_missing']['orphan_images'])}")
            
            if len(report['format_errors']['json_syntax_errors']) == expected_json_errors:
                print("   ✅ JSON语法错误检测正确")
                checks_passed += 1
            else:
                print(f"   ❌ JSON语法错误检测异常: 预期{expected_json_errors}, 实际{len(report['format_errors']['json_syntax_errors'])}")
            
            print(f"7. 验证结果: {checks_passed}/{total_checks} 通过")
            
            return checks_passed == total_checks
            
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_case_sensitivity():
    """测试大小写敏感性配置"""
    try:
        from lib.utils.logger_config import LoggerConfig
        from lib.datasets.parsers.quality_filter import QualityFilter
        
        print("=== 测试大小写敏感性配置 ===")
        print()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建大小写测试数据
            image_path = os.path.join(temp_dir, "case_test.jpg")
            annotation_path = os.path.join(temp_dir, "case_test.json")
            
            with open(image_path, 'w') as f:
                f.write("fake image")
            
            test_data = {
                "table_ind": 0,
                "quality": "QUALIFIED",  # 大写的qualified
                "cells": []
            }
            with open(annotation_path, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False)
            
            test_index = {1: {"image_path": image_path, "annotation_path": annotation_path}}
            
            # 测试大小写不敏感（默认）
            logger = LoggerConfig.setup_logger('test_case_sensitivity')
            config_insensitive = {"case_sensitive": False}
            filter_insensitive = QualityFilter(config=config_insensitive, logger=logger)
            
            result_insensitive = filter_insensitive.filter_samples(test_index, "test")
            print(f"1. 大小写不敏感 - 有效样本: {result_insensitive['statistics']['valid_samples']}")
            
            # 测试大小写敏感
            config_sensitive = {"case_sensitive": True, "accepted_values": ["qualified"]}
            filter_sensitive = QualityFilter(config=config_sensitive, logger=logger)

            # 重新创建测试索引以避免状态污染
            test_index_copy = {1: {"image_path": image_path, "annotation_path": annotation_path}}
            result_sensitive = filter_sensitive.filter_samples(test_index_copy, "test")
            print(f"2. 大小写敏感 - 有效样本: {result_sensitive['statistics']['valid_samples']}")
            
            # 验证结果
            if result_insensitive['statistics']['valid_samples'] == 1 and result_sensitive['statistics']['valid_samples'] == 0:
                print("3. ✅ 大小写敏感性配置验证通过")
                return True
            else:
                print("3. ❌ 大小写敏感性配置验证失败")
                return False
            
    except Exception as e:
        print(f"❌ 大小写敏感性测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("基于真实TableLabelMe格式的质量筛选测试 - 步骤3.3")
    print("=" * 70)
    print()
    
    success_count = 0
    total_tests = 2
    
    # 执行各项测试
    if test_real_format_quality_filtering():
        success_count += 1
    print()
    
    if test_case_sensitivity():
        success_count += 1
    print()
    
    # 生成测试报告
    print("=" * 70)
    print("测试结果汇总")
    print("=" * 70)
    print(f"总测试数: {total_tests}")
    print(f"通过: {success_count}")
    print(f"失败: {total_tests - success_count}")
    print(f"成功率: {(success_count / total_tests * 100):.1f}%")
    print()
    
    if success_count == total_tests:
        print("🎉 所有测试通过！基于真实格式的质量筛选功能验证成功！")
        return 0
    else:
        print("❌ 部分测试失败，请检查详细日志")
        return 1

if __name__ == "__main__":
    exit(main())
