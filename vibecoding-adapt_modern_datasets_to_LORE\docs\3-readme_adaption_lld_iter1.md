# LORE-TSR项目TableLabelMe数据格式支持详细设计（迭代1）

## 项目结构与总体设计

### 设计目标
为LORE-TSR项目增量支持TableLabelMe数据格式，在保持与原有COCO格式完全兼容的前提下，实现多源异构数据集的统一训练能力。迭代1专注于基础格式解析器的实现，为后续迭代奠定架构基础。

### 核心设计原则
- **简约至上**：选择满足当前需求的最简单方案
- **无侵害性**：对原项目核心逻辑零修改
- **接口一致性**：Dataset.__getitem__返回结构与COCO格式完全兼容
- **模块化扩展**：为后续迭代预留清晰的演进路径

## 目录结构树 (Directory Tree)

```
LORE-TSR/src/lib/datasets/
├── dataset_factory.py                    # [扩展] 添加TableLabelMe映射
├── dataset/
│   ├── table.py                         # [现有] COCO格式标准数据集
│   ├── table_mid.py                     # [现有] COCO格式中等尺寸数据集
│   ├── table_small.py                   # [现有] COCO格式小尺寸数据集
│   └── table_labelmev2.py               # [新增] TableLabelMe格式数据集
├── sample/
│   └── ctdet.py                         # [现有] CenterNet采样逻辑，无需修改
└── parsers/                             # [新增] 格式解析器模块
    ├── __init__.py                      # [新增] 解析器包初始化
    ├── base_parser.py                   # [新增] 解析器基类（为后续扩展）
    └── tablelabelme_parser.py           # [新增] TableLabelMe格式解析器

LORE-TSR/src/lib/
├── opts.py                              # [扩展] 添加data_config参数支持
└── configs/                             # [新增] 配置文件目录（迭代4占位）
    └── dataset_configs.py               # [占位] 外部配置文件模板
```

## 整体逻辑和交互时序图

### 核心工作流程
TableLabelMe格式的数据加载流程与现有COCO格式保持一致的调用链，仅在数据解析阶段进行格式转换。

```mermaid
sequenceDiagram
    participant Main as main.py
    participant DF as dataset_factory.py
    participant TLD as TableLabelMeDataset
    participant TLP as TableLabelMeParser
    participant CTD as CTDetDataset
    participant Model as 训练模型

    Main->>DF: get_dataset('table', 'ctdet_mid')
    DF->>TLD: 创建TableLabelMeDataset类
    DF-->>Main: 返回组合Dataset类

    Main->>TLD: dataset[index]
    TLD->>TLP: parse_file(json_path, image_path)
    TLP->>TLP: 坐标转换(bbox.p1-p4 -> segmentation)
    TLP->>TLP: 逻辑转换(lloc -> logic_axis)
    TLP->>TLP: ID生成(image_id + cell_ind)
    TLP-->>TLD: 标准化数据字典

    TLD->>CTD: __getitem__(标准化数据)
    CTD->>CTD: 数据增强和预处理
    CTD-->>TLD: 训练样本字典

    TLD-->>Main: 与COCO格式兼容的数据
    Main->>Model: 训练数据输入
```

## 数据实体结构深化

### TableLabelMe原始格式
```json
{
  "bbox": {
    "p1": {"x": 100, "y": 50},
    "p2": {"x": 200, "y": 50}, 
    "p3": {"x": 200, "y": 100},
    "p4": {"x": 100, "y": 100}
  },
  "lloc": {
    "start_row": 0,
    "end_row": 0,
    "start_col": 0,
    "end_col": 1
  },
  "cell_ind": 1,
  "quality": "合格",
  "table_ind": 0,
  "type": "cell",
  "border": true,
  "content": "示例文本"
}
```

### LORE-TSR内部标准格式
```python
{
  "image_id": 12345,           # 基于文件路径哈希生成
  "annotation_id": 123450001,  # image_id + cell_ind组合
  "category_id": 1,            # 固定为1（单元格类别）
  "segmentation": [100, 50, 200, 50, 200, 100, 100, 100],  # 转换后的坐标
  "logic_axis": [0, 0, 0, 1],  # [start_row, end_row, start_col, end_col]
  "area": 5000,                # 计算得出的面积
  "bbox": [100, 50, 100, 50],  # [x, y, width, height]格式
  "extra_info": {              # 额外信息保留
    "table_ind": 0,
    "type": "cell", 
    "border": true,
    "content": "示例文本",
    "quality": "合格"
  }
}
```

### 数据实体关系图
```mermaid
erDiagram
    TableLabelMeFile {
        string file_path
        string image_path
        json annotations
        string quality_status
    }
    
    ParsedAnnotation {
        int image_id
        int annotation_id
        int category_id
        array segmentation
        array logic_axis
        float area
        array bbox
        dict extra_info
    }
    
    TrainingData {
        tensor input
        tensor hm
        tensor wh
        tensor reg
        tensor st
        tensor logic
        dict meta
    }
    
    TableLabelMeFile ||--o{ ParsedAnnotation : "解析生成"
    ParsedAnnotation ||--|| TrainingData : "预处理转换"
```

## 配置项

### 命令行参数扩展
```bash
# TableLabelMe格式训练命令
python main.py ctdet_mid \
  --dataset table \
  --dataset_name TableLabelMe \
  --data_config /path/to/dataset_configs.py \
  --arch resfpnhalf_18 \
  --batch_size 64 \
  --num_epochs 200
```

### 新增配置参数
- `--data_config`: 外部配置文件路径（迭代4实现，当前占位）
- 复用现有参数：`--dataset table` + `--dataset_name TableLabelMe`

### 配置兼容性处理
- TableLabelMe模式下忽略`--image_dir`和`--anno_path`参数
- 数据路径信息从外部配置文件获取（迭代4实现）

## 模块化文件详解 (File-by-File Breakdown)

### src/lib/datasets/parsers/__init__.py
**a. 文件用途说明**
解析器包的初始化文件，提供统一的解析器接口导入。

**b. 文件内容**
```python
from .base_parser import BaseParser
from .tablelabelme_parser import TableLabelMeParser

__all__ = ['BaseParser', 'TableLabelMeParser']
```

### src/lib/datasets/parsers/base_parser.py
**a. 文件用途说明**
定义解析器基类，为后续支持其他数据格式提供统一接口。

**b. 文件内类图**
```mermaid
classDiagram
    class BaseParser {
        +parse_file(json_path, image_path) dict
        +validate_data(data) bool
        +generate_image_id(file_path) int
        +generate_annotation_id(image_id, cell_ind) int
    }
```

**c. 函数/方法详解**

#### parse_file方法
- **用途**: 解析单个标注文件，返回标准化数据字典
- **输入参数**: 
  - `json_path`: 标注文件路径
  - `image_path`: 对应图像文件路径
- **输出数据结构**: 标准化的数据字典或None（解析失败时）
- **实现流程**:
```mermaid
flowchart TD
    A[接收文件路径] --> B[读取JSON文件]
    B --> C{JSON格式有效?}
    C -->|否| D[返回None]
    C -->|是| E[调用子类实现]
    E --> F[数据验证]
    F --> G{验证通过?}
    G -->|否| D
    G -->|是| H[返回标准化数据]
```

#### validate_data方法
- **用途**: 验证解析后数据的完整性和正确性
- **输入参数**: `data` - 解析后的数据字典
- **输出数据结构**: bool - 验证结果
- **实现流程**: 检查必需字段存在性和数据类型正确性

#### generate_image_id方法
- **用途**: 基于文件路径生成稳定的图像ID
- **输入参数**: `file_path` - 文件路径字符串
- **输出数据结构**: int - 唯一的图像ID
- **实现流程**: 使用MD5哈希算法确保ID的稳定性和唯一性

#### generate_annotation_id方法
- **用途**: 生成全局唯一的标注ID
- **输入参数**: 
  - `image_id` - 图像ID
  - `cell_ind` - 单元格索引
- **输出数据结构**: int - 唯一的标注ID
- **实现流程**: 组合image_id和cell_ind生成唯一标识

### src/lib/datasets/parsers/tablelabelme_parser.py
**a. 文件用途说明**
TableLabelMe格式的具体解析器实现，负责格式转换和数据标准化。

**b. 文件内类图**
```mermaid
classDiagram
    BaseParser <|-- TableLabelMeParser
    class TableLabelMeParser {
        +parse_file(json_path, image_path) dict
        +convert_bbox_to_segmentation(bbox) list
        +convert_lloc_to_logic_axis(lloc) list
        +calculate_area(segmentation) float
        +extract_bbox_from_segmentation(segmentation) list
        +filter_by_quality(annotations) list
    }
```

**c. 函数/方法详解**

#### parse_file方法
- **用途**: 解析TableLabelMe格式文件，转换为LORE-TSR标准格式
- **输入参数**:
  - `json_path`: TableLabelMe JSON文件路径
  - `image_path`: 对应图像文件路径
- **输出数据结构**: 包含所有标注的标准化字典列表
- **实现流程**:
```mermaid
sequenceDiagram
    participant Parser as TableLabelMeParser
    participant File as JSON文件
    participant Converter as 格式转换器

    Parser->>File: 读取JSON内容
    File-->>Parser: 原始标注数据
    Parser->>Parser: 生成image_id
    
    loop 每个标注
        Parser->>Converter: convert_bbox_to_segmentation
        Converter-->>Parser: segmentation数组
        Parser->>Converter: convert_lloc_to_logic_axis  
        Converter-->>Parser: logic_axis数组
        Parser->>Parser: 计算面积和bbox
        Parser->>Parser: 生成annotation_id
    end
    
    Parser->>Parser: 质量筛选
    Parser-->>Parser: 标准化数据字典
```

#### convert_bbox_to_segmentation方法
- **用途**: 将四个角点坐标转换为一维segmentation数组
- **输入参数**: `bbox` - 包含p1-p4四个点的字典
- **输出数据结构**: list - [p1.x, p1.y, p2.x, p2.y, p3.x, p3.y, p4.x, p4.y]
- **实现流程**:
```mermaid
flowchart TD
    A[输入bbox字典] --> B[提取p1坐标]
    B --> C[提取p2坐标]
    C --> D[提取p3坐标]
    D --> E[提取p4坐标]
    E --> F[按顺序组合成数组]
    F --> G[返回segmentation]
```

#### convert_lloc_to_logic_axis方法
- **用途**: 将逻辑位置信息转换为logic_axis格式
- **输入参数**: `lloc` - 包含行列位置信息的字典
- **输出数据结构**: list - [start_row, end_row, start_col, end_col]
- **实现流程**: 直接提取字段值并组合成数组

#### calculate_area方法
- **用途**: 根据segmentation坐标计算单元格面积
- **输入参数**: `segmentation` - 8个坐标值的数组
- **输出数据结构**: float - 计算得出的面积
- **实现流程**: 使用多边形面积计算公式

#### filter_by_quality方法
- **用途**: 根据quality字段筛选合格的标注数据
- **输入参数**: `annotations` - 原始标注列表
- **输出数据结构**: list - 筛选后的标注列表
- **实现流程**: 过滤quality为"合格"的标注

### src/lib/datasets/dataset/table_labelmev2.py
**a. 文件用途说明**
TableLabelMe格式的数据集类，继承现有Table类的基础功能，重写数据加载逻辑。

**b. 文件内类图**
```mermaid
classDiagram
    Table <|-- TableLabelMe
    class TableLabelMe {
        +__init__(opt, split)
        +_load_annotations()
        +_build_file_index()
        +_get_image_info(index)
        +__len__()
        +convert_eval_format(all_bboxes, thresh)
        +save_results(results, save_dir, thresh)
        +run_eval(results, save_dir, thresh)
    }
```

**c. 函数/方法详解**

#### __init__方法
- **用途**: 初始化TableLabelMe数据集，设置基本参数和加载数据索引
- **输入参数**:
  - `opt`: 配置对象
  - `split`: 数据集分割（train/val/test）
- **输出数据结构**: 无（构造函数）
- **实现流程**:
```mermaid
flowchart TD
    A[接收配置参数] --> B[设置基本属性]
    B --> C[构建文件索引]
    C --> D[加载标注数据]
    D --> E[初始化完成]
```

#### _load_annotations方法
- **用途**: 加载并解析所有TableLabelMe标注文件
- **输入参数**: 无
- **输出数据结构**: 无（更新实例属性）
- **实现流程**: 遍历文件索引，调用解析器处理每个文件

#### _build_file_index方法（迭代2占位实现）
- **用途**: 构建图像-标注文件的映射索引
- **输入参数**: 无
- **输出数据结构**: dict - 文件映射字典
- **实现流程**: 当前返回固定的测试数据，迭代2实现完整功能

### src/lib/datasets/dataset_factory.py
**a. 文件用途说明**
扩展现有数据集工厂，添加TableLabelMe格式支持。

**b. 修改内容**
```python
# 在现有代码基础上添加
from .dataset.table_labelmev2 import Table as Table_labelmev2

dataset_factory = {
  'table': Table,
  'table_mid': Table_mid,
  'table_small': Table_small,
  'table_labelmev2': Table_labelmev2  # 新增映射
}
```

### src/lib/opts.py
**a. 文件用途说明**
扩展配置参数解析，添加data_config参数支持。

**b. 修改内容**
在参数定义部分添加：
```python
self.parser.add_argument('--data_config', default='',
                         help='path to external dataset configuration file')
```

## 迭代演进依据

### 架构扩展性设计
1. **解析器扩展**: `BaseParser`基类支持未来其他数据格式的解析器实现
2. **配置系统扩展**: `data_config`参数为复杂配置需求预留接口
3. **数据集类扩展**: 继承体系支持不同分辨率和格式的数据集

### 后续迭代占位
- **迭代2**: 目录扫描和索引构建功能在`_build_file_index`方法中占位
- **迭代3**: 质量筛选功能在`filter_by_quality`方法中基础实现
- **迭代4**: 配置系统集成通过`data_config`参数占位
- **迭代5**: 训练流程集成通过现有工厂模式支持
- **迭代6**: 可视化工具作为独立模块实现

### 技术债务控制
- 每个文件控制在500行以内
- 模块功能相对完整且独立
- 避免过度细化，保持适度的抽象层次

## 如何迁移现有COCO格式功能

### 代码文件对应关系
| 现有COCO格式文件 | TableLabelMe对应文件 | 迁移策略 |
|-----------------|-------------------|---------|
| `table.py` | `table_labelmev2.py` | 继承基础功能，重写数据加载逻辑 |
| `ctdet.py` | 无需修改 | 直接复用现有采样和预处理逻辑 |
| `dataset_factory.py` | 扩展映射 | 添加新的数据集类型映射 |
| `opts.py` | 扩展参数 | 添加data_config参数支持 |

### 兼容性保证
- 所有现有COCO格式功能保持不变
- 新增功能通过参数组合区分，不影响原有流程
- Dataset.__getitem__返回结构完全兼容，确保下游模块无需修改

---

**文档版本**: v1.0  
**创建日期**: 2025年1月21日  
**迭代范围**: 迭代1 - 基础格式解析器实现  
**后续迭代**: 通过固定返回值占位，保持架构完整性
