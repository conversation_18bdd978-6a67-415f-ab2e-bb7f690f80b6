#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LORE项目TEDS评价指标安装和配置脚本
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 7):
        raise RuntimeError("需要Python 3.7或更高版本")
    logger.info(f"Python版本检查通过：{sys.version}")

def install_dependencies():
    """安装依赖包"""
    logger.info("开始安装依赖包...")
    
    requirements_file = Path(__file__).parent / "lore_teds_requirements.txt"
    
    if not requirements_file.exists():
        logger.error(f"依赖文件不存在：{requirements_file}")
        return False
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)])
        
        logger.info("依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"依赖包安装失败：{e}")
        return False

def verify_installation():
    """验证安装"""
    logger.info("验证安装...")
    
    required_packages = [
        'numpy',
        'pandas', 
        'bs4',
        'tqdm'
    ]
    
    failed_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            logger.info(f"✓ {package}")
        except ImportError:
            logger.error(f"✗ {package}")
            failed_packages.append(package)
    
    # 检查TEDS库
    try:
        from table_recognition_metric import TEDS
        logger.info("✓ table_recognition_metric (TEDS引擎)")
    except ImportError:
        logger.warning("⚠ table_recognition_metric 未安装，将使用简化实现")
    
    if failed_packages:
        logger.error(f"以下包安装失败：{failed_packages}")
        return False
    
    logger.info("安装验证完成")
    return True

def create_example_config():
    """创建示例配置文件"""
    config_content = """# LORE项目TEDS评价配置文件

[DEFAULT]
# 输出目录
output_dir = ./results

# 是否只评估结构相似度（忽略内容）
structure_only = false

# 日志级别
log_level = INFO

# 批量处理时的批次大小
batch_size = 100

[PATHS]
# 数据路径配置
gt_data_dir = ./data/ground_truth
pred_data_dir = ./data/predictions

[EVALUATION]
# 评估参数
min_teds_threshold = 0.5
save_failed_cases = true
save_detailed_results = true

[VISUALIZATION]
# 可视化参数
generate_plots = true
plot_format = png
plot_dpi = 300
"""
    
    config_file = Path("lore_teds_config.ini")
    with open(config_file, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    logger.info(f"示例配置文件已创建：{config_file}")

def run_test():
    """运行测试"""
    logger.info("运行基本测试...")
    
    try:
        from lore_teds_converter import TableLabelMeToHTMLConverter, TEDSCalculator
        
        # 创建测试数据
        test_data = {
            "table_ind": 0,
            "cells": [
                {
                    "cell_ind": 0,
                    "header": True,
                    "content": [{"text": "测试列1"}],
                    "bbox": {"p1": [0, 0], "p2": [100, 0], "p3": [100, 20], "p4": [0, 20]},
                    "lloc": {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}
                },
                {
                    "cell_ind": 1,
                    "header": False,
                    "content": [{"text": "测试数据1"}],
                    "bbox": {"p1": [0, 20], "p2": [100, 20], "p3": [100, 40], "p4": [0, 40]},
                    "lloc": {"start_row": 1, "end_row": 1, "start_col": 0, "end_col": 0}
                }
            ]
        }
        
        # 测试转换
        converter = TableLabelMeToHTMLConverter()
        html_result = converter.convert_to_html(test_data)
        
        # 测试TEDS计算
        calculator = TEDSCalculator(structure_only=True)
        teds_score = calculator.calculate_teds(html_result, html_result)
        
        if teds_score == 1.0:
            logger.info("✓ 基本测试通过")
            return True
        else:
            logger.error(f"✗ 基本测试失败，TEDS分数：{teds_score}")
            return False
            
    except Exception as e:
        logger.error(f"✗ 基本测试失败：{e}")
        return False

def main():
    """主安装流程"""
    logger.info("开始LORE项目TEDS评价指标安装...")
    
    try:
        # 1. 检查Python版本
        check_python_version()
        
        # 2. 安装依赖
        if not install_dependencies():
            logger.error("依赖安装失败，退出")
            sys.exit(1)
        
        # 3. 验证安装
        if not verify_installation():
            logger.error("安装验证失败，退出")
            sys.exit(1)
        
        # 4. 创建配置文件
        create_example_config()
        
        # 5. 运行测试
        if not run_test():
            logger.error("基本测试失败，请检查安装")
            sys.exit(1)
        
        logger.info("🎉 LORE项目TEDS评价指标安装完成！")
        
        print("\n" + "="*60)
        print("安装完成！使用方法：")
        print("="*60)
        print("1. 单个样本评估：")
        print("   python lore_teds_converter.py")
        print()
        print("2. 批量评估：")
        print("   python lore_teds_evaluator.py --gt_file ground_truth.json --pred_file predictions.json")
        print()
        print("3. 查看配置：")
        print("   cat lore_teds_config.ini")
        print("="*60)
        
    except Exception as e:
        logger.error(f"安装过程中发生错误：{e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
