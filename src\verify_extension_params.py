#!/usr/bin/env python3
"""
步骤五验证脚本：验证扩展功能参数的解析
"""

def verify_extension_params():
    """验证扩展功能参数解析的正确性"""
    
    print("=== 步骤五：扩展功能参数解析验证 ===\n")
    
    try:
        from lib.opts import opts
        
        # 测试1：默认值验证
        print("1. 测试默认值:")
        opt = opts().parse(['ctdet_mid'])
        header_default = getattr(opt, 'enable_header_prediction', '未定义')
        content_default = getattr(opt, 'enable_content_prediction', '未定义')
        border_default = getattr(opt, 'enable_border_prediction', '未定义')
        
        print(f"   Header prediction (默认): {header_default}")
        print(f"   Content prediction (默认): {content_default}")
        print(f"   Border prediction (默认): {border_default}")
        
        # 验证默认值是否为False
        if header_default == False and content_default == False and border_default == False:
            print("   ✅ 默认值验证通过")
        else:
            print("   ❌ 默认值验证失败")
            return False
        
        # 测试2：启用单个参数
        print("\n2. 测试启用单个参数:")
        opt = opts().parse(['ctdet_mid', '--enable_header_prediction'])
        print(f"   Header prediction (启用): {opt.enable_header_prediction}")
        print(f"   Content prediction (默认): {opt.enable_content_prediction}")
        print(f"   Border prediction (默认): {opt.enable_border_prediction}")
        
        if opt.enable_header_prediction == True and opt.enable_content_prediction == False and opt.enable_border_prediction == False:
            print("   ✅ 单个参数启用验证通过")
        else:
            print("   ❌ 单个参数启用验证失败")
            return False
        
        # 测试3：启用多个参数
        print("\n3. 测试启用多个参数:")
        opt = opts().parse(['ctdet_mid', '--enable_header_prediction', '--enable_content_prediction'])
        print(f"   Header prediction (启用): {opt.enable_header_prediction}")
        print(f"   Content prediction (启用): {opt.enable_content_prediction}")
        print(f"   Border prediction (默认): {opt.enable_border_prediction}")
        
        if opt.enable_header_prediction == True and opt.enable_content_prediction == True and opt.enable_border_prediction == False:
            print("   ✅ 多个参数启用验证通过")
        else:
            print("   ❌ 多个参数启用验证失败")
            return False
        
        # 测试4：启用所有参数
        print("\n4. 测试启用所有参数:")
        opt = opts().parse(['ctdet_mid', '--enable_header_prediction', '--enable_content_prediction', '--enable_border_prediction'])
        print(f"   Header prediction (启用): {opt.enable_header_prediction}")
        print(f"   Content prediction (启用): {opt.enable_content_prediction}")
        print(f"   Border prediction (启用): {opt.enable_border_prediction}")
        
        if opt.enable_header_prediction == True and opt.enable_content_prediction == True and opt.enable_border_prediction == True:
            print("   ✅ 所有参数启用验证通过")
        else:
            print("   ❌ 所有参数启用验证失败")
            return False
        
        # 测试5：参数类型验证
        print("\n5. 测试参数类型:")
        opt = opts().parse(['ctdet_mid', '--enable_header_prediction'])
        print(f"   enable_header_prediction类型: {type(opt.enable_header_prediction)}")
        print(f"   enable_content_prediction类型: {type(opt.enable_content_prediction)}")
        print(f"   enable_border_prediction类型: {type(opt.enable_border_prediction)}")
        
        if (isinstance(opt.enable_header_prediction, bool) and 
            isinstance(opt.enable_content_prediction, bool) and 
            isinstance(opt.enable_border_prediction, bool)):
            print("   ✅ 参数类型验证通过")
        else:
            print("   ❌ 参数类型验证失败")
            return False
        
        print("\n=== 验证总结 ===")
        print("✅ 所有扩展参数解析验证通过")
        print("✅ 默认值正确设置为False")
        print("✅ action='store_true'机制工作正常")
        print("✅ 参数类型为布尔值")
        print("✅ 多参数组合工作正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_extension_params()
    if success:
        print("\n🎉 扩展参数解析验证完全通过！")
    else:
        print("\n❌ 扩展参数解析验证失败，需要进一步检查。")
