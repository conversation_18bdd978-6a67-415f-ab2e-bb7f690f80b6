# COCO格式数据集分析工具包 - 文档索引

## 📚 文档目录

### 🔍 核心分析报告
- **[WTW_COCO_Fields_Analysis_Report.md](./WTW_COCO_Fields_Analysis_Report.md)** - 字段详细解读报告
  - 基于联网检索的权威字段解释
  - `logic_axis` 字段的TSR应用详解
  - 技术背景与应用价值分析

### 📖 使用说明
- **[README.md](./README.md)** - 工具使用指南
  - 脚本功能介绍
  - 命令行参数说明
  - 使用示例和注意事项

## 🛠️ 工具脚本

### 分析工具
- **[analyze_coco_dataset.py](./analyze_coco_dataset.py)** - 完整数据集分析工具
- **[extract_coco_samples.py](./extract_coco_samples.py)** - 样本提取专用工具

## 📊 数据文件

### 原始数据
- **[WTW-coco-test.json](./WTW-coco-test.json)** - 原始COCO格式数据集
  - 3644个图像，348000个标注
  - 表格结构识别专用数据集

### 样本数据
- **[wtw_samples.json](./wtw_samples.json)** - 提取的2个图像样本
- **[wtw_coco_samples.json](./wtw_coco_samples.json)** - COCO格式样本数据

### 分析结果
- **[analysis_output/](./analysis_output/)** - 分析结果目录
  - `analysis_report.txt` - 数据集分析报告
  - `coco_format_samples.json` - COCO格式样本
  - `sample_data.json` - 分析格式样本
  - `full_stats.json` - 完整统计信息

## 🚀 快速开始

### 1. 查看字段含义
```bash
# 阅读详细字段解读报告
cat WTW_COCO_Fields_Analysis_Report.md
```

### 2. 分析数据集
```bash
# 运行完整分析
python analyze_coco_dataset.py --json_file WTW-coco-test.json -o analysis_output -n 2
```

### 3. 提取样本
```bash
# 提取样本并分析
python extract_coco_samples.py --input WTW-coco-test.json --output wtw_samples.json -n 2 --analyze
```

## 🔑 关键概念

### TSR (Table Structure Recognition)
表格结构识别，将图像中的表格转换为机器可理解的结构化格式。

### LORE Framework
LOgical location REgression network - 逻辑位置回归网络，用于表格结构识别的深度学习框架。

### logic_axis 字段
表格单元格逻辑位置坐标，格式：`[row_start, row_end, col_start, col_end]`

## 📞 技术支持

如需了解更多技术细节，请参考：
1. [LORE论文](https://arxiv.org/abs/2303.03730) - 逻辑位置回归的理论基础
2. [COCO官方文档](https://cocodataset.org/) - 标准COCO格式说明
3. 本工具包的详细分析报告和使用说明

---
*最后更新：2025-01-17*
