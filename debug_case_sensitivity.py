#!/usr/bin/env python3
"""
调试大小写敏感性测试
"""

import sys
import os
import json
import tempfile

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from lib.utils.logger_config import LoggerConfig
from lib.datasets.parsers.quality_filter import QualityFilter

def debug_case_sensitivity():
    """调试大小写敏感性问题"""
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试数据
        image_path = os.path.join(temp_dir, "case_test.jpg")
        annotation_path = os.path.join(temp_dir, "case_test.json")
        
        with open(image_path, 'w') as f:
            f.write("fake image")
        
        test_data = {
            "table_ind": 0,
            "quality": "QUALIFIED",  # 大写的qualified
            "cells": []
        }
        with open(annotation_path, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False)
        
        print("创建的测试文件:")
        print(f"图像文件: {image_path}")
        print(f"标注文件: {annotation_path}")
        
        # 验证文件内容
        print("\n标注文件内容:")
        with open(annotation_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
        
        # 验证JSON解析
        print("\nJSON解析测试:")
        try:
            with open(annotation_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print("JSON解析成功:", data)
            print("quality字段:", data.get('quality'))
        except Exception as e:
            print("JSON解析失败:", str(e))
        
        # 测试质量筛选器
        test_index = {1: {"image_path": image_path, "annotation_path": annotation_path}}
        
        logger = LoggerConfig.setup_logger('debug_case')
        
        # 测试大小写不敏感
        print("\n=== 测试大小写不敏感 ===")
        config_insensitive = {"case_sensitive": False}
        filter_insensitive = QualityFilter(config=config_insensitive, logger=logger)
        
        print("配置:", filter_insensitive.config)
        
        result_insensitive = filter_insensitive.filter_samples(test_index, "test")
        print("结果:", result_insensitive['statistics'])
        print("异常报告:", result_insensitive['exception_report'])
        
        # 测试大小写敏感
        print("\n=== 测试大小写敏感 ===")
        config_sensitive = {"case_sensitive": True, "accepted_values": ["qualified"]}
        filter_sensitive = QualityFilter(config=config_sensitive, logger=logger)
        
        print("配置:", filter_sensitive.config)
        
        result_sensitive = filter_sensitive.filter_samples(test_index, "test")
        print("结果:", result_sensitive['statistics'])
        print("异常报告:", result_sensitive['exception_report'])

if __name__ == "__main__":
    debug_case_sensitivity()
