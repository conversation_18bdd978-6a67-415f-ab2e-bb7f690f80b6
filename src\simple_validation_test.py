#!/usr/bin/env python3
"""
简单验证测试 - 直接测试修复后的数据加载

该脚本直接在src目录下运行，测试修复后的验证数据加载。
"""

import sys
import os
import torch
import numpy as np

# 确保能导入项目模块
sys.path.append('.')
sys.path.append('..')

def test_simple_validation():
    """简单的验证测试"""
    print("🧪 开始简单验证测试")
    
    try:
        # 导入模块
        from lib.opts import opts
        from lib.datasets.sample.ctdet import CTDetDataset as Dataset
        
        print("✅ 模块导入成功")
        
        # 创建基本选项
        opt = opts().init(['ctdet_mid', '--dataset', 'table', '--dataset_name', 'TableLabelMe'])
        opt.data_config = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/lib/configs/my_dataset_configs.py'
        opt.config_name = 'tableme_full'
        opt.exp_id = 'simple_test'
        
        print("✅ 选项配置成功")
        
        # 创建验证数据集
        print("🔧 创建验证数据集...")
        val_dataset = Dataset(opt, 'val')
        print(f"✅ 验证数据集创建成功: {len(val_dataset)}个样本")
        
        # 测试单个样本
        print("🧪 测试单个样本...")
        sample = val_dataset[0]
        print(f"✅ 样本0加载成功")
        
        # 检查样本数据
        for field_name, data in sample.items():
            if isinstance(data, np.ndarray):
                print(f"[样本0] {field_name}: shape={data.shape}, dtype={data.dtype}")
                
                # 检查整数类型
                if np.issubdtype(data.dtype, np.integer):
                    print(f"[样本0] {field_name} 整数范围: [{data.min()}, {data.max()}]")
                    
                    # 检查是否有溢出风险
                    if data.max() > 2147483647 or data.min() < -2147483648:
                        print(f"[警告] {field_name} 可能有溢出风险!")
                    else:
                        print(f"[正常] {field_name} 整数范围安全")
                        
                elif np.issubdtype(data.dtype, np.floating):
                    print(f"[样本0] {field_name} 浮点范围: [{data.min():.2f}, {data.max():.2f}]")
            else:
                print(f"[样本0] {field_name}: type={type(data)}")
        
        # 测试前5个样本
        print("\n🧪 测试前5个样本的数据加载...")
        for i in range(min(5, len(val_dataset))):
            try:
                sample = val_dataset[i]
                print(f"✅ 样本{i}加载成功")
                
                # 检查关键字段
                if 'hm_ind' in sample:
                    hm_ind = sample['hm_ind']
                    if isinstance(hm_ind, np.ndarray):
                        if hm_ind.max() > 2147483647 or hm_ind.min() < -2147483648:
                            print(f"[警告] 样本{i} hm_ind溢出: [{hm_ind.min()}, {hm_ind.max()}]")
                        else:
                            print(f"[正常] 样本{i} hm_ind安全: [{hm_ind.min()}, {hm_ind.max()}]")
                
                if 'mk_ind' in sample:
                    mk_ind = sample['mk_ind']
                    if isinstance(mk_ind, np.ndarray):
                        if mk_ind.max() > 2147483647 or mk_ind.min() < -2147483648:
                            print(f"[警告] 样本{i} mk_ind溢出: [{mk_ind.min()}, {mk_ind.max()}]")
                        else:
                            print(f"[正常] 样本{i} mk_ind安全: [{mk_ind.min()}, {mk_ind.max()}]")
                            
            except Exception as e:
                print(f"❌ 样本{i}加载失败: {e}")
                return False
        
        print("\n✅ 所有测试样本加载成功！")
        return True
        
    except Exception as e:
        print(f"❌ 验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pytorch_tensor_creation():
    """测试PyTorch张量创建"""
    print("\n🧪 测试PyTorch张量创建")
    
    try:
        # 测试各种数值范围的张量创建
        test_arrays = [
            np.array([0, 1, 2, 3], dtype=np.int32),
            np.array([1000, 2000, 3000], dtype=np.int32),
            np.array([100000, 200000, 300000], dtype=np.int32),
            np.array([0.0, 1.0, 2.0], dtype=np.float32),
        ]
        
        for i, arr in enumerate(test_arrays):
            try:
                tensor = torch.from_numpy(arr)
                print(f"✅ 测试数组{i}: {arr} -> 张量创建成功")
                print(f"   张量: shape={tensor.shape}, dtype={tensor.dtype}")
            except Exception as e:
                print(f"❌ 测试数组{i}: {arr} -> 张量创建失败: {e}")
                return False
        
        # 测试大数值数组
        large_array = np.array([2147483647, 2147483646], dtype=np.int32)
        try:
            large_tensor = torch.from_numpy(large_array)
            print(f"✅ 大数值数组: {large_array} -> 张量创建成功")
        except Exception as e:
            print(f"❌ 大数值数组张量创建失败: {e}")
            return False
        
        print("✅ PyTorch张量创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ PyTorch张量测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始简单验证修复测试")
    print("=" * 50)
    
    # 运行测试
    test_results = []
    
    test_results.append(test_simple_validation())
    test_results.append(test_pytorch_tensor_creation())
    
    # 生成报告
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    
    test_names = ["验证数据加载", "PyTorch张量创建"]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {name}: {status}")
    
    if all(test_results):
        print("\n🎉 所有测试通过！修复可能有效！")
        print("💡 建议现在尝试运行原始训练命令")
        return 0
    else:
        print("\n⚠️  部分测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
