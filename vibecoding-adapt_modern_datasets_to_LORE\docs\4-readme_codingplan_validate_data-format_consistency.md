# LORE-TSR数据格式一致性验证编码计划

## 项目概述

**项目目标**: 验证同一条数据的COCO格式和TableLabelMe格式在输入模型前是否经过完全相同的处理，产生相同的输入张量。可选验证是否得到相同的loss结果。

**验证策略**: 简化的数据处理一致性验证

**核心验证点**:
1. **必须验证**: 数据预处理后的输入张量一致性
2. **可选验证**: 模型loss计算一致性

## 技术背景分析

### 数据处理流程
基于对LORE-TSR项目的分析，数据处理流程如下：

1. **数据格式转换**:
   - TableLabelMe: `bbox.p1-p4` → `segmentation`, `lloc` → `logic_axis`
   - COCO: 直接使用`segmentation`和`logic_axis`

2. **CTDetDataset.__getitem__处理**:
   - 图像加载和预处理
   - 标注解析和坐标变换
   - 生成训练目标（hm, wh, reg, logic等）

3. **最终输出的batch结构**:
```python
batch = {
    'input': inp,      # 图像张量 - 主要验证目标
    'hm': hm,         # 热力图目标
    'wh': wh,         # 边界框尺寸
    'logic': log_ax,  # 逻辑坐标
    'hm_mask': hm_mask,
    'hm_ind': hm_ind
}
```

## 简化验证方案

### 验证步骤

#### 步骤1: 创建简单验证脚本
**影响文件**:
- `[待创建]` `scripts/validate_data_consistency.py`

**具体操作**:
1. 创建单个验证脚本文件
2. 集成数据加载和对比功能
3. 支持命令行参数配置

**复用已有代码**:
- 复用`src/lib/datasets/dataset_factory.py`的数据集创建逻辑
- 复用`src/lib/datasets/sample/ctdet.py`的数据处理逻辑

**验证命令**:
```bash
python scripts/validate_data_consistency.py \
  --coco_data_path /path/to/coco/data \
  --tablelabelme_data_path /path/to/tablelabelme/data \
  --sample_count 5 \
  --check_loss
```

#### 步骤2: 实现核心验证逻辑
**脚本内部功能模块**:

1. **数据加载模块**:
```python
def load_coco_sample(data_path, sample_id):
    # 使用现有COCO数据集加载逻辑
    pass

def load_tablelabelme_sample(data_path, sample_id):
    # 使用现有TableLabelMe数据集加载逻辑
    pass
```

2. **数据对比模块**:
```python
def compare_input_tensors(coco_batch, tablelabelme_batch):
    # 对比input张量是否完全一致
    # 对比其他关键字段（hm, wh, logic等）
    pass
```

3. **可选loss验证模块**:
```python
def compare_loss_computation(model, coco_batch, tablelabelme_batch):
    # 可选：对比loss计算结果
    pass
```

#### 步骤3: 验证脚本实现要点

**主要验证逻辑**:
1. **同一数据的两种格式准备**:
   - 确保使用完全相同的原始数据
   - 分别通过COCO和TableLabelMe格式加载

2. **数据处理一致性验证**:
   - 对比`input`张量（图像预处理结果）
   - 对比`hm`、`wh`、`logic`等关键字段
   - 计算数值差异并报告

3. **可选loss验证**:
   - 如果提供预训练模型，验证loss计算一致性
   - 对比总loss和各分项loss

**验证标准**:
- 输入张量差异 < 1e-6
- 关键字段数值完全一致
- 可选：loss差异 < 1e-6

## 脚本设计要点

### 命令行参数设计
```bash
python scripts/validate_data_consistency.py \
  --coco_data_path /path/to/coco/data \
  --tablelabelme_data_path /path/to/tablelabelme/data \
  --sample_count 5 \
  --model_path /path/to/model.pth \  # 可选，用于loss验证
  --check_loss \                     # 可选，是否验证loss
  --output_dir ./validation_results
```

### 核心函数设计
```python
def main():
    # 1. 参数解析
    # 2. 数据加载设置
    # 3. 逐样本验证
    # 4. 结果汇总和报告

def validate_single_sample(sample_id):
    # 1. 加载COCO格式数据
    # 2. 加载TableLabelMe格式数据
    # 3. 对比处理结果
    # 4. 可选：对比loss计算

def compare_batch_data(batch1, batch2):
    # 对比两个batch的所有字段

def generate_report(results):
    # 生成验证报告
```

## 验证流程设计

### 主要验证步骤
1. **环境准备**: 设置Python路径，导入必要模块
2. **数据加载**: 分别加载同一数据的两种格式
3. **数据处理**: 通过CTDetDataset.__getitem__处理数据
4. **结果对比**: 逐字段对比处理结果
5. **可选loss验证**: 如果提供模型，验证loss计算
6. **报告生成**: 输出验证结果

### 验证逻辑伪代码
```python
def validate_data_consistency():
    for sample_id in sample_list:
        # 加载COCO格式
        coco_batch = load_coco_sample(sample_id)

        # 加载TableLabelMe格式
        tablelabelme_batch = load_tablelabelme_sample(sample_id)

        # 对比关键字段
        input_diff = compare_tensors(coco_batch['input'], tablelabelme_batch['input'])
        hm_diff = compare_tensors(coco_batch['hm'], tablelabelme_batch['hm'])
        logic_diff = compare_tensors(coco_batch['logic'], tablelabelme_batch['logic'])

        # 可选：loss验证
        if model_provided:
            coco_loss = calculate_loss(model, coco_batch)
            tablelabelme_loss = calculate_loss(model, tablelabelme_batch)
            loss_diff = abs(coco_loss - tablelabelme_loss)

        # 记录结果
        results.append({
            'sample_id': sample_id,
            'input_diff': input_diff,
            'hm_diff': hm_diff,
            'logic_diff': logic_diff,
            'loss_diff': loss_diff if model_provided else None
        })

    # 生成报告
    generate_report(results)
```

## 目标目录结构树

```text
LORE-TSR-adapt/
├── scripts/
│   └── validate_data_consistency.py                   # [待创建] 单一验证脚本
└── validation_results/                                # [待创建] 验证结果输出目录
    ├── validation_report.txt                          # [待创建] 文本验证报告
    └── validation_log.txt                             # [待创建] 详细验证日志
```

## 验证成功标准

### 必须验证项
1. **输入张量一致性**: `input`字段数值差异 < 1e-6
2. **关键字段一致性**: `hm`、`wh`、`logic`等字段完全一致
3. **数据结构一致性**: batch字典结构和字段类型一致

### 可选验证项
1. **损失计算一致性**: 如果提供模型，总损失差异 < 1e-6
2. **分项损失一致性**: 各子损失（hm_loss, wh_loss, ax_loss等）差异 < 1e-6

### 验证覆盖率
- 至少验证5个不同的数据样本
- 涵盖不同复杂度的表格数据

## 简化验证流程图

```mermaid
flowchart TD
    A[开始验证] --> B[准备测试数据]
    B --> C[加载COCO格式数据]
    C --> D[加载TableLabelMe格式数据]
    D --> E[对比输入张量]
    E --> F{是否验证loss?}
    F -->|是| G[加载预训练模型]
    F -->|否| H[生成验证报告]
    G --> I[计算两种格式的loss]
    I --> J[对比loss结果]
    J --> H
    H --> K[验证完成]

    style A fill:#e1f5fe
    style K fill:#c8e6c9
    style E fill:#fff3e0
    style I fill:#fff3e0
```

## 核心验证逻辑图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Script as 验证脚本
    participant COCO as COCO数据集
    participant TableLabelMe as TableLabelMe数据集
    participant Model as LORE模型(可选)

    User->>Script: 启动验证
    Script->>COCO: 加载样本数据
    COCO-->>Script: 返回处理后的batch
    Script->>TableLabelMe: 加载相同样本
    TableLabelMe-->>Script: 返回处理后的batch

    Script->>Script: 对比input张量
    Script->>Script: 对比hm、wh、logic字段

    alt 如果启用loss验证
        Script->>Model: 加载预训练模型
        Script->>Model: COCO batch前向传播
        Model-->>Script: 返回loss
        Script->>Model: TableLabelMe batch前向传播
        Model-->>Script: 返回loss
        Script->>Script: 对比loss结果
    end

    Script-->>User: 输出验证报告
```

## 实施步骤

### 步骤1: 创建验证脚本
**文件**: `scripts/validate_data_consistency.py`
**功能**: 单一脚本包含所有验证逻辑

### 步骤2: 测试脚本功能
**验证命令**:
```bash
# 基础功能测试
python scripts/validate_data_consistency.py --help

# 数据一致性验证（不含loss）
python scripts/validate_data_consistency.py \
  --coco_data_path /path/to/coco/data \
  --tablelabelme_data_path /path/to/tablelabelme/data \
  --sample_count 5

# 完整验证（包含loss）
python scripts/validate_data_consistency.py \
  --coco_data_path /path/to/coco/data \
  --tablelabelme_data_path /path/to/tablelabelme/data \
  --model_path /path/to/model.pth \
  --sample_count 5 \
  --check_loss
```

### 步骤3: 验证结果分析
**输出文件**:
- `validation_results/validation_report.txt`: 验证结果汇总
- `validation_results/validation_log.txt`: 详细验证日志

## 预期交付物

### 核心脚本
- **验证脚本**: `validate_data_consistency.py` (约300-400行)
- **使用说明**: 命令行参数和配置选项说明

### 验证报告
- **数据一致性报告**: 输入张量和关键字段对比结果
- **可选loss报告**: 如果启用，包含loss计算对比结果

### 验证标准
- 输入张量差异 < 1e-6 ✅
- 关键字段完全一致 ✅
- 可选：loss差异 < 1e-6 ✅

---

**重要说明**: 简化方案专注于核心验证目标，避免过度复杂的架构设计。脚本设计为单文件，便于在远程服务器上部署和执行。
