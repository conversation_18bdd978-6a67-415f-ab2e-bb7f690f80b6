#!/usr/bin/env python3
"""
ID生成修复验证脚本

验证img_id和annotation_id的生成是否在安全范围内，
确保不会导致PyTorch整数溢出。

使用方法:
python test_id_generation_fix.py

作者: AI Assistant
日期: 2025-07-23
"""

import sys
import os
import hashlib

# 添加项目路径
sys.path.append('src')
sys.path.append('src/lib')

def test_file_scanner_id_generation():
    """测试FileScanner的ID生成"""
    print("🧪 测试1: FileScanner ID生成安全性")
    
    try:
        from datasets.parsers.file_scanner import FileScanner
        
        scanner = FileScanner()
        
        # 测试多个文件路径
        test_paths = [
            "/path/to/image1.jpg",
            "/very/long/path/to/some/deep/directory/structure/image2.jpg", 
            "/another/path/with/special/chars/图像_测试.jpg",
            "C:\\Windows\\Path\\image3.jpg",
            "/short.jpg"
        ]
        
        MAX_SAFE_INT = 2147483647
        all_safe = True
        
        for path in test_paths:
            try:
                img_id = scanner._generate_image_id(path)
                is_safe = 0 <= img_id <= MAX_SAFE_INT
                
                print(f"✅ 路径: {path}")
                print(f"   ID: {img_id}, 安全: {is_safe}")
                
                if not is_safe:
                    all_safe = False
                    print(f"❌ ID超出安全范围!")
                    
            except Exception as e:
                print(f"❌ ID生成失败: {path}, 错误: {e}")
                all_safe = False
        
        if all_safe:
            print("✅ FileScanner ID生成安全性测试通过")
            return True
        else:
            print("❌ FileScanner ID生成存在安全问题")
            return False
            
    except Exception as e:
        print(f"❌ FileScanner测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_base_parser_id_generation():
    """测试BaseParser的ID生成"""
    print("\n🧪 测试2: BaseParser ID生成安全性")
    
    try:
        from datasets.parsers.base_parser import BaseParser
        
        parser = BaseParser()
        
        # 测试image_id生成
        test_paths = [
            "/path/to/image1.jpg",
            "/very/long/path/image2.jpg",
            "/short.jpg"
        ]
        
        MAX_SAFE_INT = 2147483647
        all_safe = True
        
        print("📋 测试image_id生成:")
        for path in test_paths:
            try:
                img_id = parser.generate_image_id(path)
                is_safe = 0 <= img_id <= MAX_SAFE_INT
                
                print(f"✅ 路径: {path}")
                print(f"   image_id: {img_id}, 安全: {is_safe}")
                
                if not is_safe:
                    all_safe = False
                    
            except Exception as e:
                print(f"❌ image_id生成失败: {path}, 错误: {e}")
                all_safe = False
        
        # 测试annotation_id生成
        print("\n📋 测试annotation_id生成:")
        test_cases = [
            (1000, 1),
            (2000000, 500),
            (MAX_SAFE_INT // 10000, 9999),  # 接近边界的情况
            (100000000, 1000)  # 可能导致溢出的情况
        ]
        
        for img_id, cell_ind in test_cases:
            try:
                ann_id = parser.generate_annotation_id(img_id, cell_ind)
                is_safe = 0 <= ann_id <= MAX_SAFE_INT
                
                print(f"✅ img_id={img_id}, cell_ind={cell_ind}")
                print(f"   annotation_id: {ann_id}, 安全: {is_safe}")
                
                if not is_safe:
                    all_safe = False
                    
            except Exception as e:
                print(f"❌ annotation_id生成失败: img_id={img_id}, cell_ind={cell_ind}, 错误: {e}")
                all_safe = False
        
        if all_safe:
            print("✅ BaseParser ID生成安全性测试通过")
            return True
        else:
            print("❌ BaseParser ID生成存在安全问题")
            return False
            
    except Exception as e:
        print(f"❌ BaseParser测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_hash_collision_probability():
    """测试哈希冲突概率"""
    print("\n🧪 测试3: 哈希冲突概率评估")
    
    try:
        # 生成大量ID，检查冲突率
        test_paths = [f"/test/path/image_{i}.jpg" for i in range(10000)]
        
        generated_ids = set()
        collisions = 0
        
        for path in test_paths:
            # 模拟修复后的ID生成逻辑
            hash_obj = hashlib.md5(path.encode('utf-8'))
            img_id = int(hash_obj.hexdigest()[:8], 16)
            
            # 确保在安全范围内
            MAX_SAFE_INT = 2147483647
            if img_id > MAX_SAFE_INT:
                img_id = img_id % MAX_SAFE_INT
            
            if img_id in generated_ids:
                collisions += 1
            else:
                generated_ids.add(img_id)
        
        collision_rate = collisions / len(test_paths) * 100
        
        print(f"✅ 测试样本数: {len(test_paths)}")
        print(f"✅ 唯一ID数: {len(generated_ids)}")
        print(f"✅ 冲突数: {collisions}")
        print(f"✅ 冲突率: {collision_rate:.4f}%")
        
        # 冲突率应该很低（< 1%）
        if collision_rate < 1.0:
            print("✅ 哈希冲突概率测试通过")
            return True
        else:
            print("⚠️  哈希冲突率较高，但仍可接受")
            return True
            
    except Exception as e:
        print(f"❌ 哈希冲突测试失败: {e}")
        return False

def test_pytorch_tensor_compatibility():
    """测试PyTorch张量兼容性"""
    print("\n🧪 测试4: PyTorch张量兼容性")
    
    try:
        import torch
        import numpy as np
        
        # 生成一些测试ID
        test_ids = []
        for i in range(100):
            path = f"/test/image_{i}.jpg"
            hash_obj = hashlib.md5(path.encode('utf-8'))
            img_id = int(hash_obj.hexdigest()[:8], 16)
            
            # 确保在安全范围内
            MAX_SAFE_INT = 2147483647
            if img_id > MAX_SAFE_INT:
                img_id = img_id % MAX_SAFE_INT
                
            test_ids.append(img_id)
        
        # 转换为numpy数组
        id_array = np.array(test_ids, dtype=np.int32)
        
        # 转换为PyTorch张量
        id_tensor = torch.from_numpy(id_array)
        
        # 测试collate操作
        batch_tensor = torch.stack([id_tensor])
        
        print(f"✅ 测试ID数量: {len(test_ids)}")
        print(f"✅ ID范围: [{min(test_ids)}, {max(test_ids)}]")
        print(f"✅ numpy数组: shape={id_array.shape}, dtype={id_array.dtype}")
        print(f"✅ PyTorch张量: shape={id_tensor.shape}, dtype={id_tensor.dtype}")
        print(f"✅ 批次张量: shape={batch_tensor.shape}")
        
        print("✅ PyTorch张量兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ PyTorch兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始ID生成修复验证测试")
    print("=" * 60)
    
    # 运行所有测试
    test_results = []
    
    test_results.append(test_file_scanner_id_generation())
    test_results.append(test_base_parser_id_generation())
    test_results.append(test_hash_collision_probability())
    test_results.append(test_pytorch_tensor_compatibility())
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    test_names = [
        "FileScanner ID生成安全性",
        "BaseParser ID生成安全性",
        "哈希冲突概率评估",
        "PyTorch张量兼容性"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(test_results)} 测试通过")
    
    if all(test_results):
        print("🎉 所有测试通过！ID生成修复验证成功！")
        print("💡 现在可以尝试运行训练命令，img_id溢出问题应该已解决")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步检查修复代码")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
