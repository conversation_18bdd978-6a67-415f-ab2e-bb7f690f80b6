#!/usr/bin/env python
from __future__ import absolute_import, division, print_function

import math
import os
import torch
from torch import nn
from torch.autograd import Function
from torch.autograd.function import once_differentiable
from torch.nn.modules.utils import _pair

# 尝试导入预编译的 _ext 模块
try:
    import _ext as _backend
except ImportError as e:
    # 如果导入失败，尝试从当前目录加载
    try:
        import sys
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.append(current_dir)
        
        # 尝试设置LD_LIBRARY_PATH
        torch_lib_path = os.path.join(os.path.dirname(torch.__file__), 'lib')
        os.environ['LD_LIBRARY_PATH'] = f"{torch_lib_path}:{os.environ.get('LD_LIBRARY_PATH', '')}"
        
        import _ext as _backend
        print("Loaded _ext from current directory")
    except ImportError as e:
        # 如果仍然失败，尝试使用PyTorch内置的deform_conv2d
        print(f"WARNING: Could not import _ext module: {e}")
        print("Falling back to PyTorch's built-in deform_conv2d")
        _backend = None

# 如果有torchvision，尝试导入内置的deform_conv2d作为备选
try:
    from torchvision.ops import deform_conv2d as tv_deform_conv2d
    has_torchvision_deform = True
except ImportError:
    has_torchvision_deform = False


class _DCNv2(Function):
    @staticmethod
    def forward(
        ctx, input, offset, mask, weight, bias, stride, padding, dilation, deformable_groups
    ):
        # 确保输入在GPU上（如果使用GPU）
        ctx.stride = _pair(stride)
        ctx.padding = _pair(padding)
        ctx.dilation = _pair(dilation)
        ctx.kernel_size = _pair(weight.shape[2:4])
        ctx.deformable_groups = deformable_groups
        
        # 检查是否在GPU上
        if input.is_cuda:
            if _backend is None or not hasattr(_backend, 'dcn_v2_forward'):
                if has_torchvision_deform:
                    print("WARNING: Using torchvision's deform_conv2d as fallback for CUDA")
                    output = tv_deform_conv2d(
                        input, offset, weight, bias, 
                        stride=stride, padding=padding, dilation=dilation, mask=mask
                    )
                    ctx.save_for_backward(input, offset, mask, weight, bias)
                    return output
                else:
                    raise RuntimeError("DCNv2 CUDA implementation not available")
        
        # 调用后端实现
        output = _backend.dcn_v2_forward(
            input,
            weight,
            bias,
            offset,
            mask,
            ctx.kernel_size[0],
            ctx.kernel_size[1],
            ctx.stride[0],
            ctx.stride[1],
            ctx.padding[0],
            ctx.padding[1],
            ctx.dilation[0],
            ctx.dilation[1],
            ctx.deformable_groups,
        )
        ctx.save_for_backward(input, offset, mask, weight, bias)
        return output

    @staticmethod
    @once_differentiable
    def backward(ctx, grad_output):
        input, offset, mask, weight, bias = ctx.saved_tensors
        
        # 检查是否在GPU上
        if grad_output.is_cuda:
            if _backend is None or not hasattr(_backend, 'dcn_v2_backward'):
                raise RuntimeError("DCNv2 CUDA implementation not available for backward")
        
        grad_input, grad_offset, grad_mask, grad_weight, grad_bias = _backend.dcn_v2_backward(
            input,
            weight,
            bias,
            offset,
            mask,
            grad_output,
            ctx.kernel_size[0],
            ctx.kernel_size[1],
            ctx.stride[0],
            ctx.stride[1],
            ctx.padding[0],
            ctx.padding[1],
            ctx.dilation[0],
            ctx.dilation[1],
            ctx.deformable_groups,
        )

        return grad_input, grad_offset, grad_mask, grad_weight, grad_bias, None, None, None, None


dcn_v2_conv = _DCNv2.apply


class DCNv2(nn.Module):
    def __init__(
        self,
        in_channels,
        out_channels,
        kernel_size,
        stride,
        padding,
        dilation=1,
        deformable_groups=1,
    ):
        super(DCNv2, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.kernel_size = _pair(kernel_size)
        self.stride = _pair(stride)
        self.padding = _pair(padding)
        self.dilation = _pair(dilation)
        self.deformable_groups = deformable_groups

        self.weight = nn.Parameter(torch.Tensor(out_channels, in_channels, *self.kernel_size))
        self.bias = nn.Parameter(torch.Tensor(out_channels))
        self.reset_parameters()

    def reset_parameters(self):
        n = self.in_channels
        for k in self.kernel_size:
            n *= k
        stdv = 1.0 / math.sqrt(n)
        self.weight.data.uniform_(-stdv, stdv)
        self.bias.data.zero_()

    def forward(self, input, offset, mask):
        assert (
            2 * self.deformable_groups * self.kernel_size[0] * self.kernel_size[1]
            == offset.shape[1]
        )
        assert self.deformable_groups * self.kernel_size[0] * self.kernel_size[1] == mask.shape[1]
        
        # 如果在GPU上但没有CUDA实现，尝试使用torchvision
        if input.is_cuda and (_backend is None or not hasattr(_backend, 'dcn_v2_forward')):
            if has_torchvision_deform:
                return tv_deform_conv2d(
                    input, 
                    offset, 
                    self.weight, 
                    self.bias, 
                    stride=self.stride,
                    padding=self.padding, 
                    dilation=self.dilation, 
                    mask=mask
                )
            else:
                raise RuntimeError("DCNv2 CUDA implementation not available and no fallback found")
        
        return dcn_v2_conv(
            input,
            offset,
            mask,
            self.weight,
            self.bias,
            self.stride,
            self.padding,
            self.dilation,
            self.deformable_groups,
        )


class DCN(DCNv2):
    def __init__(
        self,
        in_channels,
        out_channels,
        kernel_size,
        stride,
        padding,
        dilation=1,
        deformable_groups=1,
    ):
        super(DCN, self).__init__(
            in_channels, out_channels, kernel_size, stride, padding, dilation, deformable_groups
        )

        channels_ = self.deformable_groups * 3 * self.kernel_size[0] * self.kernel_size[1]
        self.conv_offset_mask = nn.Conv2d(
            self.in_channels,
            channels_,
            kernel_size=self.kernel_size,
            stride=self.stride,
            padding=self.padding,
            bias=True,
        )
        self.init_offset()

    def init_offset(self):
        self.conv_offset_mask.weight.data.zero_()
        self.conv_offset_mask.bias.data.zero_()

    def forward(self, input):
        out = self.conv_offset_mask(input)
        o1, o2, mask = torch.chunk(out, 3, dim=1)
        offset = torch.cat((o1, o2), dim=1)
        mask = torch.sigmoid(mask)
        
        # 如果在GPU上但没有CUDA实现，尝试使用torchvision
        if input.is_cuda and (_backend is None or not hasattr(_backend, 'dcn_v2_forward')):
            if has_torchvision_deform:
                return tv_deform_conv2d(
                    input, 
                    offset, 
                    self.weight, 
                    self.bias, 
                    stride=self.stride,
                    padding=self.padding, 
                    dilation=self.dilation, 
                    mask=mask
                )
            else:
                raise RuntimeError("DCNv2 CUDA implementation not available and no fallback found")
            
        return dcn_v2_conv(
            input,
            offset,
            mask,
            self.weight,
            self.bias,
            self.stride,
            self.padding,
            self.dilation,
            self.deformable_groups,
        )
