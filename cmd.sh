
## wired
python main.py ctdet --dataset_name table --exp_id training_wtw --dataset_name WTW --image_dir /aipdf-mlp/shared/tsr_dataset/WTW/all_images/images --wiz_4ps --wiz_stacking --wiz_pairloss --tsfm_layers 3 --stacking_layers 3 --batch_size 6 --master_batch 6 --arch dla_34 --lr 1e-4 --K 500 --MK 1000 --num_epochs 100 --lr_step '70, 90' --gpus 0 --num_workers 16 --val_intervals 5


## wireless
python main.py ctdet_mid --dataset table_mid --exp_id train_wireless --dataset_name WTW --image_dir /aipdf-mlp/shared/tsr_dataset/WTW/all_images/images --wiz_2dpe --wiz_stacking --tsfm_layers 4 --stacking_layers 4 --batch_size 8 --master_batch 12 --arch dla_34 --lr 1e-4 --K 500 --MK 1000 --num_epochs 200 --lr_step '100, 160' --gpus 0 --num_workers 16 --val_intervals 5
### arc resfpnhalf_18
python main.py ctdet_mid --dataset table_mid --exp_id train_wireless --dataset_name WTW --image_dir /aipdf-mlp/shared/tsr_dataset/WTW/all_images/images --wiz_2dpe --wiz_stacking --tsfm_layers 4 --stacking_layers 4 --batch_size 6 --master_batch 6 --arch resfpnhalf_18 --lr 1e-4 --K 500 --MK 1000 --num_epochs 200 --lr_step '100, 160' --gpus 0 --num_workers 16 --val_intervals 5



### adapt

#### wireless resfpnhalf_18
python main.py ctdet_mid --dataset table_mid --exp_id train_wireless --dataset_name WTW --image_dir /aipdf-mlp/shared/tsr_dataset/WTW/all_images/images --wiz_2dpe --wiz_stacking --tsfm_layers 4 --stacking_layers 4 --batch_size 6 --master_batch 6 --arch resfpnhalf_18 --lr 1e-4 --K 500 --MK 1000 --num_epochs 200 --lr_step '100, 160' --gpus 0 --num_workers 16 --val_intervals 5

python main.py ctdet_mid --dataset table_mid --dataset_name TableLabelMe --data_config /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/lib/configs/my_dataset_configs.py --config_name tableme_full --exp_id train_tableme --wiz_2dpe --wiz_stacking --tsfm_layers 4 --stacking_layers 4 --batch_size 6 --master_batch 6 --arch resfpnhalf_18 --lr 1e-4 --K 500 --MK 1000 --num_epochs 200 --lr_step '100, 160' --gpus 0 --num_workers 16 --val_intervals 5
python main.py ctdet_mid --dataset table --dataset_name TableLabelMe --data_config /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/lib/configs/my_dataset_configs.py --config_name tableme_full --exp_id train_tableme --wiz_2dpe --wiz_stacking --tsfm_layers 4 --stacking_layers 4 --batch_size 6 --master_batch 6 --arch resfpnhalf_18 --lr 1e-4 --K 500 --MK 1000 --num_epochs 200 --lr_step '100, 160' --gpus 0 --num_workers 16 --val_intervals 5
python main.py ctdet_mid --dataset table_labelmev2 --dataset_name TableLabelMe --data_config /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/lib/configs/my_dataset_configs.py --config_name tableme_full --exp_id train_tableme --wiz_2dpe --wiz_stacking --tsfm_layers 4 --stacking_layers 4 --batch_size 6 --master_batch 6 --arch resfpnhalf_18 --lr 1e-4 --K 500 --MK 1000 --num_epochs 200 --lr_step '100, 160' --gpus 0 --num_workers 16 --val_intervals 5


## check
python main.py ctdet_mid --dataset table_mid --dataset_name TableLabelMe --data_config /aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/lib/configs/my_dataset_configs.py --config_name tableme_full --exp_id train_tableme_check --wiz_2dpe --wiz_stacking --tsfm_layers 4 --stacking_layers 4 --batch_size 8 --master_batch 12 --arch resfpnhalf_18 --lr 1e-4 --K 500 --MK 1000 --num_epochs 200 --lr_step '100, 160' --gpus 0 --num_workers 4 --val_intervals 1
