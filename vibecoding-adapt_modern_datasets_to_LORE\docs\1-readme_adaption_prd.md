# LORE-TSR项目TableLabelMe数据格式支持需求文档

## 1. 需求概述

### 1.1 项目背景
LORE-TSR项目当前支持COCO格式的表格结构识别数据集。为了扩展项目的数据支持能力，需要增量支持新的TableLabelMe数据格式，同时保持与原有COCO格式的完全兼容性。

### 1.2 核心目标
- **增量支持**：在不影响原有COCO格式支持的前提下，新增TableLabelMe格式支持
- **算法一致性**：确保两种数据格式在训练流程中产生完全一致的结果
- **无侵害性**：对原项目的数据预处理、模型定义、损失函数等核心逻辑零修改
- **使用体验**：除参数配置外，使用体验与原项目保持一致

### 1.3 业务价值
- 支持多源异构数据集的统一训练
- 提升数据集适配的灵活性和扩展性
- 为未来数据格式扩展奠定架构基础

## 2. 数据格式对比分析

### 2.1 原有COCO格式特征
- **组织方式**：集中式存储，共享images目录，统一标注文件
- **标注格式**：扩展COCO格式，包含segmentation和logic_axis字段
- **文件结构**：
  ```
  ├── data_dir 
  │   ├── images 
  │   └── json 
  │       ├──train.json 
  │       └──test.json
  ```

### 2.2 新增TableLabelMe格式特征
- **组织方式**：分布式存储，图像与标注文件一对一对应
- **标注格式**：TableLabelMe格式，使用bbox.p1-p4和lloc字段
- **文件结构**：
  ```
  src_dir/
  ├── part_0001/
  │   ├── xxx.jpg/png
  │   ├── xxx.json 或 xxx_table_annotation.json
  │   └── ...
  ├── part_0002/
  │   ├── xxx.jpg/png
  │   ├── xxx.json 或 xxx_table_annotation.json
  │   └── ...
  └── ...
  ```

### 2.3 关键字段映射关系

| TableLabelMe字段 | LORE-TSR内部字段 | 转换逻辑 |
|-----------------|-----------------|---------|
| bbox.p1-p4 | segmentation | 提取四个角点坐标，按顺序组合成一维数组 |
| lloc | logic_axis | 将start_row, end_row, start_col, end_col组合 |
| cell_ind | annotation id | image_id + cell_ind生成全局唯一ID |
| 文件名关联 | image_id | 基于文件路径哈希生成唯一ID |
| quality | 数据筛选 | 只加载"合格"标记的样本 |
| table_ind, type, border, content等 | 额外信息保留 | 作为Dataset.__getitem__返回字典的额外字段 |

## 3. 功能需求

### 3.1 配置系统扩展

#### 3.1.1 数据格式识别
- 利用现有"--dataset"和"--dataset_name"参数区分数据格式
- TableLabelMe格式参数取值：
  - `--dataset table`
  - `--dataset_name TableLabelMe`

#### 3.1.2 多源数据配置
- 新增"--data_config"参数，接受绝对路径
- 指向configs/dataset_configs.py文件
- 配置文件结构示例：
  ```python
  DATASET_PATH_CONFIGS = {
      'tableme_full': {
          'train': [
              '/path/to/TabRecSet_chinese/train',
              '/path/to/TabRecSet_english/train',
              '/path/to/WTW/train',
              '/path/to/TALOCRTable/train'
          ],
          'val': [
              '/path/to/TabRecSet_chinese/val',
              '/path/to/TabRecSet_english/val',
              '/path/to/WTW/val',
              '/path/to/TALOCRTable/val'
          ]
      }
  }
  ```

#### 3.1.3 参数兼容性处理
- 使用TableLabelMe格式时忽略"--image_dir"和"--anno_path"参数
- 其他参数保持原有逻辑不变

### 3.2 数据加载功能

#### 3.2.1 数据扫描与索引
- 遍历配置文件中指定的所有数据目录
- 扫描每个目录下的part_xxxx子目录
- 构建图像与标注文件的映射关系
- 在Dataset初始化时一次性构建完整索引

#### 3.2.2 质量筛选机制
- 解析标注文件中的"quality"字段（位于JSON顶层）
- 只加载quality为"合格"的样本参与训练
- 记录跳过样本的详细信息

#### 3.2.3 格式转换处理
- 将TableLabelMe格式转换为LORE-TSR内部统一格式
- 保持数据预处理、坐标变换等流程完全一致
- 保留原始格式的额外信息供未来扩展使用

### 3.3 错误处理与日志

#### 3.3.1 异常情况处理
- 图像文件无对应标注文件：跳过该样本
- 标注文件无对应图像文件：跳过该样本
- 数据目录不存在或为空：跳过该目录，继续处理其他目录
- 数据格式异常：记录异常信息，不中断训练流程

#### 3.3.2 日志记录要求
- 在现有训练日志基础上添加数据加载相关信息
- 记录内容包括：时间戳、文件路径、跳过原因、加载统计等
- 同时输出到控制台和日志文件

### 3.4 可视化验证工具

#### 3.4.1 功能要求
- 独立脚本实现
- 支持批量处理完整数据集
- 生成原始TableLabelMe标注与转换后LORE-TSR格式的对比可视化

#### 3.4.2 输出要求
- 为每个测试样本生成单独的可视化图像
- 采用并排对比的展示方式
- 确保转换前后的标注在视觉效果上完全一致

## 4. 非功能需求

### 4.1 兼容性要求
- **向后兼容**：完全保持对原有COCO格式的支持
- **算法一致性**：两种格式训练的模型性能必须一致
- **接口一致性**：Dataset.__getitem__返回的数据结构保持兼容

### 4.2 性能要求
- **初始化性能**：在Dataset初始化时一次性构建索引，后续访问高效
- **内存使用**：暂不考虑大数据量场景的内存优化
- **加载性能**：暂不考虑IO性能优化

### 4.3 可维护性要求
- **代码隔离**：新增功能对原有代码零侵害
- **配置管理**：通过独立配置文件管理多源数据路径
- **扩展性**：为未来支持其他数据格式预留架构空间

## 5. 使用场景

### 5.1 训练场景
```bash
python main.py ctdet_mid \
    --dataset table \
    --exp_id train_wireless \
    --dataset_name TableLabelMe \
    --data_config tableme_full \
    --wiz_2dpe \
    --wiz_stacking \
    --tsfm_layers 4 \
    --stacking_layers 4 \
    --batch_size 6 \
    --master_batch 6 \
    --arch resfpnhalf_18 \
    --lr 1e-4 \
    --K 500 \
    --MK 1000 \
    --num_epochs 200 \
    --lr_step '100, 160' \
    --gpus 0 \
    --num_workers 16 \
    --val_intervals 5
```

### 5.2 推理场景
- 支持使用TableLabelMe格式训练的模型进行推理
- 需要指定相应的数据格式参数
- 推理流程与原有COCO格式保持一致

### 5.3 可视化验证场景
- 独立运行可视化验证脚本
- 批量处理指定数据集
- 生成格式转换正确性验证报告

## 6. 约束条件

### 6.1 技术约束
- 不修改原有的数据预处理、模型定义、损失函数逻辑
- 不改变现有的训练和推理流程
- 保持与原项目完全相同的算法性能

### 6.2 数据约束
- 用户保证训练集和验证集使用相同的数据格式
- 用户保证多源数据中不存在重复的图像文件
- TableLabelMe格式数据必须包含必要的字段（bbox、lloc、quality等）

### 6.3 使用约束
- 配置文件路径必须使用绝对路径
- 数据目录结构必须符合规定的part_xxxx格式
- 标注文件命名必须与图像文件名对应（支持.json或_table_annotation.json后缀）

## 7. 验收标准

### 7.1 基本功能验收
- 能够成功加载TableLabelMe格式数据集
- 能够完成完整的训练流程
- 训练过程中的数据统计和日志记录正确

### 7.2 兼容性验收
- 原有COCO格式功能完全不受影响
- 两种格式训练的模型在相同数据上性能一致
- 所有现有的训练和推理脚本正常工作

### 7.3 质量验收
- 可视化验证工具确认格式转换的正确性
- 错误处理机制能够正确处理各种异常情况
- 日志记录完整准确，便于问题排查

## 8. 交付物

### 8.1 代码交付
- 扩展后的数据加载模块
- 配置文件模板（configs/dataset_configs.py）
- 可视化验证工具脚本

### 8.2 文档交付
- TableLabelMe格式支持使用指南
- COCO格式与TableLabelMe格式对比说明
- 配置示例和命令行示例
- 迁移指南和故障排除指南

### 8.3 测试交付
- 格式转换正确性验证报告
- 兼容性测试报告
- 性能对比测试报告

---

**文档版本**：v1.0  
**创建日期**：2025年1月21日  
**最后更新**：2025年1月21日
