#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LORE项目TEDS批量评估工具

支持批量处理LORE模型输出结果，计算TEDS评价指标
"""

import json
import os
import argparse
import pandas as pd
from pathlib import Path
from typing import List, Dict, Tuple
import logging
from tqdm import tqdm
import time

from lore_teds_converter import TableLabelMeToHTMLConverter, TEDSCalculator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LORETEDSEvaluator:
    """LORE项目TEDS评估器"""
    
    def __init__(self, structure_only: bool = False, output_dir: str = "./results"):
        """
        初始化评估器
        
        Args:
            structure_only: 是否只评估结构相似度
            output_dir: 结果输出目录
        """
        self.converter = TableLabelMeToHTMLConverter()
        self.calculator = TEDSCalculator(structure_only=structure_only)
        self.structure_only = structure_only
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 评估结果存储
        self.results = []
        self.failed_cases = []
    
    def load_ground_truth(self, gt_file: str) -> Dict[str, Dict]:
        """
        加载真实标签数据
        
        Args:
            gt_file: 真实标签文件路径（JSON格式）
            
        Returns:
            以图像ID为键的真实标签字典
        """
        logger.info(f"加载真实标签：{gt_file}")
        
        with open(gt_file, 'r', encoding='utf-8') as f:
            if gt_file.endswith('.jsonl'):
                # JSONL格式：每行一个JSON对象
                gt_data = {}
                for line in f:
                    if line.strip():
                        item = json.loads(line.strip())
                        # 假设有image_id字段作为键
                        image_id = item.get('image_id', item.get('table_ind', len(gt_data)))
                        gt_data[str(image_id)] = item
            else:
                # 标准JSON格式
                data = json.load(f)
                if isinstance(data, list):
                    gt_data = {str(i): item for i, item in enumerate(data)}
                else:
                    gt_data = data
        
        logger.info(f"加载完成：{len(gt_data)}个真实标签")
        return gt_data
    
    def load_predictions(self, pred_file: str) -> Dict[str, Dict]:
        """
        加载预测结果数据
        
        Args:
            pred_file: 预测结果文件路径
            
        Returns:
            以图像ID为键的预测结果字典
        """
        logger.info(f"加载预测结果：{pred_file}")
        
        with open(pred_file, 'r', encoding='utf-8') as f:
            if pred_file.endswith('.jsonl'):
                pred_data = {}
                for line in f:
                    if line.strip():
                        item = json.loads(line.strip())
                        image_id = item.get('image_id', item.get('table_ind', len(pred_data)))
                        pred_data[str(image_id)] = item
            else:
                data = json.load(f)
                if isinstance(data, list):
                    pred_data = {str(i): item for i, item in enumerate(data)}
                else:
                    pred_data = data
        
        logger.info(f"加载完成：{len(pred_data)}个预测结果")
        return pred_data
    
    def evaluate_single_pair(self, gt_data: Dict, pred_data: Dict, image_id: str) -> Dict:
        """
        评估单个样本对
        
        Args:
            gt_data: 真实标签数据
            pred_data: 预测结果数据
            image_id: 图像ID
            
        Returns:
            评估结果字典
        """
        try:
            start_time = time.time()
            
            # 转换真实标签为HTML
            gt_html = self.converter.convert_to_html(gt_data)
            
            # 转换预测结果为HTML
            pred_html = self.converter.convert_to_html(pred_data)
            
            # 计算TEDS分数
            teds_score = self.calculator.calculate_teds(gt_html, pred_html)
            
            processing_time = time.time() - start_time
            
            result = {
                'image_id': image_id,
                'teds_score': teds_score,
                'processing_time': processing_time,
                'gt_cells_count': len(gt_data.get('cells', [])),
                'pred_cells_count': len(pred_data.get('cells', [])),
                'status': 'success'
            }
            
            return result
            
        except Exception as e:
            logger.error(f"评估失败 {image_id}: {str(e)}")
            return {
                'image_id': image_id,
                'teds_score': 0.0,
                'processing_time': 0.0,
                'gt_cells_count': len(gt_data.get('cells', [])),
                'pred_cells_count': len(pred_data.get('cells', [])),
                'status': 'failed',
                'error': str(e)
            }
    
    def evaluate_batch(self, gt_file: str, pred_file: str) -> Dict:
        """
        批量评估
        
        Args:
            gt_file: 真实标签文件
            pred_file: 预测结果文件
            
        Returns:
            评估统计结果
        """
        logger.info("开始批量评估...")
        
        # 加载数据
        gt_data = self.load_ground_truth(gt_file)
        pred_data = self.load_predictions(pred_file)
        
        # 找到共同的图像ID
        common_ids = set(gt_data.keys()) & set(pred_data.keys())
        logger.info(f"共同样本数量：{len(common_ids)}")
        
        if not common_ids:
            raise ValueError("真实标签和预测结果没有共同的样本ID")
        
        # 批量评估
        self.results = []
        self.failed_cases = []
        
        for image_id in tqdm(common_ids, desc="评估进度"):
            result = self.evaluate_single_pair(
                gt_data[image_id], 
                pred_data[image_id], 
                image_id
            )
            
            self.results.append(result)
            
            if result['status'] == 'failed':
                self.failed_cases.append(result)
        
        # 计算统计信息
        stats = self._calculate_statistics()
        
        # 保存结果
        self._save_results(stats)
        
        return stats
    
    def _calculate_statistics(self) -> Dict:
        """计算评估统计信息"""
        successful_results = [r for r in self.results if r['status'] == 'success']
        
        if not successful_results:
            return {
                'total_samples': len(self.results),
                'successful_samples': 0,
                'failed_samples': len(self.failed_cases),
                'success_rate': 0.0,
                'mean_teds': 0.0,
                'std_teds': 0.0,
                'min_teds': 0.0,
                'max_teds': 0.0,
                'median_teds': 0.0
            }
        
        teds_scores = [r['teds_score'] for r in successful_results]
        processing_times = [r['processing_time'] for r in successful_results]
        
        import numpy as np
        
        stats = {
            'total_samples': len(self.results),
            'successful_samples': len(successful_results),
            'failed_samples': len(self.failed_cases),
            'success_rate': len(successful_results) / len(self.results),
            'mean_teds': np.mean(teds_scores),
            'std_teds': np.std(teds_scores),
            'min_teds': np.min(teds_scores),
            'max_teds': np.max(teds_scores),
            'median_teds': np.median(teds_scores),
            'mean_processing_time': np.mean(processing_times),
            'total_processing_time': np.sum(processing_times)
        }
        
        return stats
    
    def _save_results(self, stats: Dict) -> None:
        """保存评估结果"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果
        results_df = pd.DataFrame(self.results)
        results_file = self.output_dir / f"teds_results_{timestamp}.csv"
        results_df.to_csv(results_file, index=False)
        logger.info(f"详细结果已保存：{results_file}")
        
        # 保存统计信息
        stats_file = self.output_dir / f"teds_statistics_{timestamp}.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=2, ensure_ascii=False)
        logger.info(f"统计信息已保存：{stats_file}")
        
        # 保存失败案例
        if self.failed_cases:
            failed_file = self.output_dir / f"failed_cases_{timestamp}.json"
            with open(failed_file, 'w', encoding='utf-8') as f:
                json.dump(self.failed_cases, f, indent=2, ensure_ascii=False)
            logger.info(f"失败案例已保存：{failed_file}")
    
    def print_summary(self, stats: Dict) -> None:
        """打印评估摘要"""
        print("\n" + "="*50)
        print("LORE项目TEDS评估结果摘要")
        print("="*50)
        print(f"总样本数：{stats['total_samples']}")
        print(f"成功样本数：{stats['successful_samples']}")
        print(f"失败样本数：{stats['failed_samples']}")
        print(f"成功率：{stats['success_rate']:.2%}")
        print(f"平均TEDS分数：{stats['mean_teds']:.4f}")
        print(f"TEDS标准差：{stats['std_teds']:.4f}")
        print(f"TEDS最小值：{stats['min_teds']:.4f}")
        print(f"TEDS最大值：{stats['max_teds']:.4f}")
        print(f"TEDS中位数：{stats['median_teds']:.4f}")
        print(f"平均处理时间：{stats.get('mean_processing_time', 0):.3f}秒")
        print(f"总处理时间：{stats.get('total_processing_time', 0):.2f}秒")
        print("="*50)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LORE项目TEDS评估工具")
    parser.add_argument("--gt_file", required=True, help="真实标签文件路径")
    parser.add_argument("--pred_file", required=True, help="预测结果文件路径")
    parser.add_argument("--output_dir", default="./results", help="结果输出目录")
    parser.add_argument("--structure_only", action="store_true", help="只评估结构相似度")
    
    args = parser.parse_args()
    
    # 创建评估器
    evaluator = LORETEDSEvaluator(
        structure_only=args.structure_only,
        output_dir=args.output_dir
    )
    
    # 执行评估
    try:
        stats = evaluator.evaluate_batch(args.gt_file, args.pred_file)
        evaluator.print_summary(stats)
        
    except Exception as e:
        logger.error(f"评估过程中发生错误：{str(e)}")
        raise

if __name__ == "__main__":
    main()
