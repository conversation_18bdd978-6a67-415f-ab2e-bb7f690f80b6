# 2024年至今开源无线表格结构识别模型调研报告

## 1. 引言

随着数字化转型的深入，从图像中准确提取表格信息变得越来越重要。传统的表格识别方法在处理无线表格（即没有明确可见边框的表格）和复杂表格结构（如合并单元格）时面临挑战。本报告旨在调研2024年至今开源的无线表格结构识别模型和相关论文，重点关注其在表格单元格border属性预测、模型架构、单元格逻辑关系表示以及损失函数设计方面的创新和特点。本次调研主要分析了TFLOP、MuTabNet和UniTable三个代表性模型。

## 2. 模型概述

| 模型名称 | 发表时间/会议 | 主要特点 | 核心创新 | 开源代码 | 论文链接 |
|---|---|---|---|---|---|
| TFLOP | IJCAI 2024 | 将文本区域预测和匹配重构为直接指向问题，避免错位；采用span-aware对比监督。 | Layout Pointer Mechanism，Span-aware Contrastive Supervision | [GitHub](https://github.com/UpstageAI/TFLOP) | [IJCAI 2024](https://www.ijcai.org/proceedings/2024/105) |
| MuTabNet | ICDAR 2024 | 端到端表格OCR模型，同时进行结构和内容识别；引入多单元格解码器和双向互学习。 | Multi-Cell Content Decoder，Bidirectional Mutual Learning | [GitHub](https://github.com/JG1VPP/MuTabNet) | [arXiv](https://arxiv.org/abs/2404.13268) |
| UniTable | NeurIPS TRL Workshop, 2024 | 统一训练范式和训练目标，通过自监督预训练和语言建模实现端到端表格识别。 | Self-Supervised Pretraining，Unified Language Modeling Objective | [GitHub](https://github.com/poloclub/unitable) | [项目页面](https://shengyun-peng.github.io/papers/unitable)，[arXiv](https://arxiv.org/abs/2403.04822) |

## 3. 详细对比分析

### 3.1 模型架构

**TFLOP** [1]：
TFLOP的架构基于Donut模型，主要由图像编码器（Swin Transformer）、布局编码器、逻辑结构解码器（BART架构）和布局指针模块组成。图像编码器负责提取视觉特征，布局编码器处理文本区域边界框信息。逻辑结构解码器自回归地生成表格标签序列（OTSL-tag序列，与HTML有1对1映射）。其核心创新在于**布局指针模块（Layout Pointer Mechanism）**，它将解码器输出的表格数据标签与对应的文本区域边界框关联起来，从而解决了传统方法中文本区域错位的问题，避免了额外的后处理。

**MuTabNet** [2]：
MuTabNet是一个端到端的表格OCR模型，采用**分层Transformer**架构。它旨在同时进行表格结构识别和单元格内容识别。其关键组成部分包括一个HTML解码器用于结构识别，以及一个**多单元格内容解码器（Multi-Cell Content Decoder）**，用于推断多个单元格并从周围单元格获取上下文信息。此外，它还引入了**双向互学习机制（Bidirectional Mutual Learning）**，通过前向和后向解码器之间的协同学习，强制模型关注前一个和后一个单元格，以更好地理解表格结构。

**UniTable** [3]：
UniTable提出了一个统一的训练框架，其架构的核心在于**自监督预训练（Self-Supervised Pretraining, SSP）**和**统一的语言建模（Unified Language Modeling）**目标。模型包含一个视觉编码器和一个任务解码器。视觉编码器通过预测被遮蔽的表格图像进行自监督预训练，然后与任务解码器一起在监督数据集上进行微调。UniTable将提取表格结构、单元格内容和单元格边界框这三个任务统一为一个语言建模任务，即模型的输入是原始像素图像，输出是HTML格式的token序列。

### 3.2 单元格逻辑关系表示

**TFLOP** [1]：
TFLOP通过其**布局指针机制**直接将预测的表格数据标签（如HTML的`<td>`标签）与文本区域的边界框关联起来。这意味着模型能够直接理解哪个文本内容属于哪个单元格，并根据OTSL-tag序列（与HTML有1对1映射）表示单元格的行、列以及合并信息。对于复杂表格（包含rowspan或colspan），TFLOP采用**Span-aware Contrastive Supervision**来增强模型对表格布局的理解，从而更准确地识别和表示单元格之间的合并关系。

**MuTabNet** [2]：
MuTabNet通过输出HTML标签和单元格内容来表示单元格的逻辑关系，包括合并单元格。其**多单元格内容解码器**和**双向互学习机制**的引入，旨在更好地理解单元格之间的上下文关系，从而更准确地识别逻辑结构。模型能够从单元格布局中推断表格结构，即使在没有明确边界的情况下也能处理复杂表格。

**UniTable** [3]：
UniTable通过**语言建模**的方式来表示单元格的逻辑关系。模型输出的是HTML格式的token序列，HTML本身就包含了丰富的逻辑结构信息，例如`<tr>`、`<td>`、`<th>`以及`rowspan`和`colspan`等属性。这种端到端的语言建模方法使得模型能够自然地生成包含逻辑关系的HTML结构，从而表示单元格之间的连接和合并关系。

### 3.3 Border属性预测

这三个模型都没有直接预测表格单元格的“border属性”。它们的目标都是将表格图像转换为机器可读的HTML格式，而HTML本身就包含了表格的结构信息，包括单元格的边界。因此，border属性的预测是隐含在HTML结构输出中的，而不是作为单独的预测任务。

- **TFLOP** [1]：通过预测单元格的精确边界框和其逻辑结构（HTML标签），间接地提供了构建带有正确border的HTML表格所需的所有信息。它专注于解决文本区域与预测区域的错位问题，这有助于更准确地定义单元格的物理范围，从而在生成HTML时能正确地绘制边界。
- **MuTabNet** [2]：明确提到能够处理“无线表格”（invisible borders），这意味着它能够从单元格布局中理解表格结构，即使没有明确的可见边界。通过预测单元格的精确位置和逻辑关系，它能够推断出单元格之间的边界信息，并在输出HTML时体现出来。
- **UniTable** [3]：同样通过生成HTML格式的表格来隐式地处理border信息。它能够成功重建包含多个跨度单元格的复杂表格，这表明它能够准确地识别单元格的物理边界和逻辑连接，从而为生成带有正确border的HTML提供了基础。

总结来说，这些模型通过准确识别单元格的物理边界（边界框）和逻辑关系（HTML结构，包括合并单元格），提供了足够的信息来构建带有正确border的HTML表格。它们没有一个独立的“border预测”模块，而是将border信息作为表格结构识别的副产品或隐式结果。

### 3.4 损失函数设计

**TFLOP** [1]：
TFLOP的训练目标由三部分组成：
1.  **Tag Classification Loss (L_cls)**：用于监督解码器的标签分类，采用负对数似然（negative-log likelihood）。
2.  **Layout Pointer Loss (L_ptr)**：包含主Layout Pointer Loss（评估每个边界框与对应表格数据标签的关联性）和Empty Table Data Tag Loss (L_empty_ptr)（处理空表格数据标签）。
3.  **Span-aware Contrastive Loss (L_contr)**：用于增强模型对复杂表格结构的理解，包含行方向和列方向的对比损失。它通过span-coefficient来调整对比监督，该系数表示边界框之间基于跨度重叠的接近程度。
总损失函数是这三部分损失的线性组合。

**MuTabNet** [2]：
论文中没有详细说明具体的损失函数，但根据其端到端和互学习的特性，推测可能包含：
-   **结构识别损失**：用于HTML标签的预测，可能是交叉熵损失。
-   **内容识别损失**：用于单元格内容的OCR识别，可能是序列到序列的损失。
-   **互学习损失**：用于协调前向和后向解码器之间的学习，可能是一种一致性损失。

**UniTable** [3]：
UniTable的训练目标是**语言建模**，因此其核心损失函数是**交叉熵损失（Cross-Entropy Loss）**，用于预测下一个token。这是一种任务无关的统一训练目标，适用于同时提取表格结构、单元格内容和边界框。此外，由于采用了自监督预训练，预训练阶段可能还涉及到特定的损失，例如掩码图像建模（Masked Image Modeling）的损失。

## 4. 总结与建议

本次调研的TFLOP、MuTabNet和UniTable模型代表了2024年至今开源无线表格结构识别领域的最新进展。它们共同的趋势是采用Transformer架构，并致力于实现端到端的表格结构和内容识别，尤其关注对复杂表格和无线表格的处理。

在**border属性预测**方面，这些模型都没有一个独立的模块来直接预测border，而是通过精确的单元格边界框检测和HTML结构输出（包含`rowspan`和`colspan`等属性）来隐式地表示border信息。这意味着，如果模型能够准确地识别单元格的物理位置和逻辑关系，那么生成带有正确border的HTML表格将是水到渠成的事情。

对于您训练新的表格结构识别模型的需求，以下是一些建议：

1.  **考虑端到端方法**：TFLOP和UniTable都采用了端到端的方法，直接从图像生成结构化输出，这简化了流程并减少了中间步骤可能引入的误差。MuTabNet也强调了端到端。
2.  **关注无线表格和复杂结构**：这三个模型都强调了对无线表格和合并单元格等复杂结构的处理能力。在设计模型时，应特别考虑如何有效地从视觉信息中推断这些隐式结构。
3.  **利用预训练和自监督学习**：UniTable的成功表明自监督预训练在表格识别领域具有巨大潜力，可以利用大量的未标注表格图像来提升模型性能。TFLOP的对比学习也属于这一范畴。
4.  **HTML作为输出格式**：将HTML作为最终输出格式是一个主流且有效的方法，因为它能够自然地表示表格的结构和内容，并包含了border所需的所有信息。
5.  **损失函数设计**：
    -   **TFLOP的损失函数**提供了很好的借鉴，特别是其布局指针损失和span-aware对比损失，这些对于处理单元格关联和复杂结构非常有效。
    -   **UniTable的语言建模损失**则提供了一种更统一的视角，将所有任务归结为序列生成，这可能简化模型设计。

在实际训练中，您可以根据具体的数据集特点和性能要求，选择或结合这些模型的优点。例如，如果您的数据集中存在大量文本区域错位问题，TFLOP的布局指针机制将非常有价值；如果需要处理大量无线表格和复杂合并单元格，MuTabNet的双向互学习和多单元格解码器，以及TFLOP的span-aware对比监督，都值得深入研究。UniTable的统一语言建模方法则提供了一个简洁而强大的框架。

## 5. 参考文献

[1] Khang, M., & Hong, T. (2024). TFLOP: Table Structure Recognition Framework with Layout Pointer Mechanism. *Proceedings of the Thirty-Third International Joint Conference on Artificial Intelligence (IJCAI-24)*, 947-955. [https://www.ijcai.org/proceedings/2024/105](https://www.ijcai.org/proceedings/2024/105)

[2] Kawakatsu, T. (2024). Multi-Cell Decoder and Mutual Learning for Table Structure and Character Recognition. *Document Analysis and Recognition - ICDAR 2024*. [https://arxiv.org/abs/2404.13268](https://arxiv.org/abs/2404.13268)

[3] Peng, S., Lee, S., Wang, X., Balasubramaniyan, R., & Chau, D. H. (2024). UniTable: Towards a Unified Framework for Table Structure Recognition via Self-Supervised Pretraining. *NeurIPS TRL Workshop, 2024*. [https://shengyun-peng.github.io/papers/unitable](https://shengyun-peng.github.io/papers/unitable)




### 3.5 LORE模型分析

**LORE** (LOgical location REgression network) [4] 提出了一种新的表格结构识别范式，将TSR建模为**逻辑位置回归问题**，并结合了表格单元格的空间位置回归。与传统的通过预测单元格邻接关系或生成标记语言序列的方法不同，LORE旨在直接预测每个单元格的逻辑位置（起始行、结束行、起始列、结束列）。

**模型架构**：
LORE的核心是一个级联回归框架，用于更好地建模逻辑位置之间的依赖关系和约束。它首先定位表格中的单元格，然后同时预测单元格的逻辑位置和空间位置。这种方法避免了复杂的后处理或耗时的序列解码过程，使得推理过程是并行的网络前向传播。

**单元格逻辑关系表示**：
LORE通过直接回归每个单元格的逻辑坐标（起始行、结束行、起始列、结束列）来表示单元格之间的逻辑关系。这些逻辑坐标能够完整地定义单元格在表格网格中的位置和跨度（即合并单元格信息）。论文中强调，这种逻辑坐标是机器可理解的表格结构的良好定义表示，足以重建表格。

**Border属性预测**：
LORE论文中没有明确提及“border属性预测”。然而，由于LORE能够精确预测每个单元格的**空间位置**（边界框）和**逻辑位置**（行/列索引及跨度），这意味着它提供了足够的信息来推断单元格的物理边界。与TFLOP、MuTabNet和UniTable类似，LORE也通过其核心预测结果来隐式地支持border信息的还原。当模型准确地识别出每个单元格的精确边界和它们在表格中的相对位置以及合并信息时，生成带有正确border的HTML或其他结构化输出将是直接的。因此，border的存在与否可以根据单元格的相邻关系和合并状态从预测的逻辑和空间位置中推导出来。

**损失函数设计**：
LORE的损失函数设计围绕其逻辑位置回归和空间位置回归的目标。论文中提到了“inter-cell and intra-cell supervisions”，这表明损失函数可能包含：
-   **空间位置回归损失**：用于预测单元格的边界框坐标（例如L1或L2损失）。
-   **逻辑位置回归损失**：用于预测单元格的起始行、结束行、起始列、结束列。这可能是一个回归损失，或者结合了分类损失（如果将行/列索引视为类别）。
-   **级联回归框架的特定损失**：用于建模逻辑位置之间的依赖关系。
-   **对比损失或结构化损失**：虽然论文没有明确说明，但为了确保预测的逻辑位置符合表格的整体结构和约束，可能会引入额外的结构化损失或一致性损失。

## 5. 参考文献

[4] Xing, H., Gao, F., Long, R., Bu, J., Zheng, Q., Li, L., ... & Yu, Z. (2023). LORE: Logical Location Regression Network for Table Structure Recognition. *Proceedings of the AAAI Conference on Artificial Intelligence*, 37(11), 13320-13328. [https://arxiv.org/abs/2303.03730](https://arxiv.org/abs/2303.03730)




### 3.6 TableBorderNet模型分析

**TableBorderNet** [5] 是一个专门用于**表格边框提取**的语义分割框架，旨在复杂视觉条件下精确提取表格边框。该模型的核心创新在于其对**拓扑一致性**的考虑，并引入了**拓扑感知损失函数（topology-aware loss function）**，该函数在训练过程中明确惩罚结构不连续性，以确保提取的边框在拓扑上是完整的和一致的。

**模型架构**：
TableBorderNet是一个语义分割框架，通过引导卷积特征提取沿明确的行和列方向来捕获结构上下文，从而实现更准确的表格边框描绘。它包含三个核心组件：
1.  **SelfSynth Training**：一种生成式自监督训练策略，从完整的表格样本合成有缺陷的表格样本，以减少手动标注成本并增强对各种表格缺陷场景的适应性。
2.  **SliceConv Enhancement**：一种网络架构，通过行-列切片卷积和特征聚合的拓扑特征增强模块，扩展上下文感知能力，实现精确的边框提取。
3.  **TopoLoss**：拓扑感知损失函数，明确惩罚结构不连续性，确保复杂或退化输入中的拓扑一致性。

**单元格逻辑关系表示**：
TableBorderNet主要关注的是表格的物理结构，即边框的提取。它通过精确的边框提取来间接支持单元格的逻辑关系识别。一旦边框被准确识别，单元格的划分和合并信息就可以通过后续的逻辑分析（例如，基于连通组件或交点分析）来推断。

**Border属性预测**：
与之前分析的模型不同，TableBorderNet是**直接预测表格边框**的。它将边框提取视为一个语义分割任务，输出的是像素级别的边框预测。其拓扑感知损失函数对于还原每个单元格的border是否存在至关重要，因为它强制模型学习边框的连通性和完整性，从而能够准确地描绘出即使是退化或不连续的边框。

**损失函数设计**：
TableBorderNet引入了**拓扑感知损失函数（TopoLoss）**，该函数在训练过程中明确惩罚结构不连续性。这意味着它不仅关注像素级别的分类准确性，还关注预测边框的拓扑正确性，例如确保边框是连续的，并且在逻辑上是封闭的。此外，作为语义分割模型，它可能还包含标准的像素级分类损失（如交叉熵损失）。

## 5. 参考文献

[5] Yang, J., Zhou, S., Li, X., Huang, Y., & Jiang, H. (2025). TableBorderNet: A Table Border Extraction Network Considering Topological Regularity. *Sensors*, 25(13), 3899. [https://pmc.ncbi.nlm.nih.gov/articles/PMC12251624/](https://pmc.ncbi.nlm.nih.gov/articles/PMC12251624/)





## SEMv3: A Fast and Robust Approach to Table Separation Line Detection

**论文链接:** https://arxiv.org/html/2405.11862v1

**模型概述:** SEMv3 (SEM: Split, Embed and Merge) 是一种快速且鲁棒的表格分离线检测方法，遵循“split-and-merge”范式。它旨在解决无线表格和变形表格带来的挑战。

**模型架构:**
*   **Split Stage:** 引入 Keypoint Offset Regression (KOR) 模块，通过直接回归每条线相对于其关键点提议的偏移量来有效检测表格分离线。与基于分割的方法不同，KOR无需复杂的mask-to-line后处理。与之前的线回归方法不同，KOR不直接预测线关键点的绝对坐标，而是预测相对于提议的偏移量，降低了回归难度。
*   **Merge Stage:** 定义了一系列合并操作（merge actions），基于表格网格高效地描述表格结构。合并模块只使用了简单的卷积层来预测网格的合并动作。

**如何预测单元格的border属性:**
SEMv3主要关注表格分离线的检测，这些分离线可以被视为表格的边框。它通过 Keypoint Offset Regression (KOR) 模块来预测这些线的存在和位置。KOR将分离线表示为一系列关键点，并通过回归这些关键点相对于提议的偏移量来精确定位线。这是一种线检测的方法，可以用来识别表格的水平和垂直边框。对于用户的数据标注中包含的每个单元格的border信息（0表示border不可见，1表示border可见），SEMv3可以通过其分离线检测结果来推断单元格的边框存在性。例如，如果一个单元格的某个方向（上、下、左、右）存在检测到的分离线，则可以认为该方向的border是可见的（1），否则是不可见的（0）。

**如何表示单元格之间的逻辑关系:**
SEMv3在“merge”阶段通过定义“merge actions”来描述表格结构，这些操作用于合并过分割的表格网格以形成最终的表格单元格。这意味着模型在检测到分离线（边框）后，会进一步处理这些线形成的网格，并根据逻辑关系进行合并，从而得到最终的单元格结构。这表明SEMv3能够处理单元格的合并情况，从而间接表示单元格之间的逻辑关系。

**损失函数设计:**
论文中未直接给出KOR模块和合并模块的具体损失函数细节，但提到KOR模块是从线回归的角度来处理分离线检测的，因此可能会使用回归损失（如L1或L2损失）。合并模块则通过预测合并动作来描述表格结构，可能使用分类损失（如交叉熵损失）来预测每个网格的合并状态。

**开源代码仓库:**
论文中明确提供了开源代码仓库链接：https://github.com/Chunchunwumu/SEMv3

**与LORE的借鉴点:**
1.  **分离线检测作为边框预测：** LORE可以借鉴SEMv3的KOR模块，将单元格的四条边（上、下、左、右）视为需要检测的分离线。对于每个单元格，可以预测其四条边的存在性（0/1分类）以及这些边的精确位置（回归）。这比LORE目前仅预测逻辑结构和物理位置更进一步，直接提供了边框信息。
2.  **关键点偏移回归：** KOR通过回归关键点偏移量来精确定位线，这种方法可以用于LORE中更精确地预测单元格的边界，从而更好地还原border信息。
3.  **“Split-and-Merge”范式：** LORE可以考虑在预测出单元格的逻辑结构和物理位置后，增加一个后处理步骤，利用类似SEMv3的“merge actions”来精细化单元格的边界，特别是处理合并单元格的边框显示问题。
4.  **开源代码：** SEMv3提供了开源代码，可以作为LORE改进的参考实现，研究其KOR模块和合并策略的具体细节。

**总结:** SEMv3通过其分离线检测和合并机制，为LORE在单元格border属性预测方面提供了新的思路。特别是KOR模块，可以作为LORE中用于直接预测单元格四条边存在性及其精确位置的组件。结合用户数据中包含的border信息，可以训练一个分类器来预测每个单元格每条边的可见性，并结合回归来预测其精确位置。




## Table Extraction With Table Data Using VGG-19 Deep Learning Model

**论文链接:** https://www.preprints.org/manuscript/202411.1627/v1

**模型概述:** 本文提出了一个名为Table Extraction Model (TEM) 的深度学习模型，旨在结合表格检测和表格结构识别任务。该模型利用预训练的VGG-19特性进行微调，以提高整体性能。它强调通过像素级预测来识别表格和列区域，而不是仅仅预测它们的边框。

**模型架构:**
*   **基础网络:** 采用预训练的VGG-19作为基础网络，用于从输入图像中提取特征。
*   **多任务架构:** 论文中提到TEM是一个“advanced deep multi-task architecture”，专门用于检测表格和列，以及识别结构。这暗示了模型可能包含多个输出头，分别处理不同的任务。

**如何预测单元格的border属性:**
论文中明确提到：“Our approach involves employing pixel-wise prediction to identify table and column areas, rather than solely predicting their borders.” 这表明该模型主要通过像素级预测来识别表格和列的区域，而不是直接预测边框。虽然这可以间接推断出单元格的边界，但它没有像TableBorderNet那样直接将边框作为语义分割的目标。因此，它可能无法直接提供每个单元格四条边（上、下、左、右）的border存在信息。

**如何表示单元格之间的逻辑关系:**
论文中提到模型旨在识别表格结构，并且“organizing them into rows and columns based on their vertical and horizontal overlaps”。这表明模型通过分析检测到的表格和列区域的几何关系来推断行和列的结构，进而表示单元格之间的逻辑关系。对于合并单元格，可能需要额外的后处理步骤来识别。

**损失函数设计:**
论文中没有详细说明具体的损失函数设计，但作为多任务模型，很可能包含用于表格检测（如边界框回归和分类损失）和结构识别（如像素级分类或分割损失）的组合损失。

**开源代码仓库:**
论文中未提供开源代码仓库链接。

**与LORE的借鉴点:**
1.  **像素级区域识别：** 尽管该模型不直接预测边框，但其像素级识别表格和列区域的方法可以为LORE提供一个思路。LORE可以在预测逻辑结构和物理位置的基础上，增加一个分支来预测每个单元格的像素级区域掩码。然后，通过分析这些区域掩码的边界，可以间接推断出边框信息。
2.  **VGG-19作为特征提取器：** VGG-19作为成熟的特征提取器，可以为LORE提供强大的视觉特征。LORE可以考虑在其骨干网络中集成VGG-19或其他更现代的CNN架构，以增强其对图像视觉信息的理解能力。
3.  **多任务学习：** LORE可以借鉴多任务学习的思想，将逻辑结构预测、物理位置回归和单元格border属性预测（如果能转换为像素级区域预测）整合到一个统一的框架中，通过共享特征来提高整体性能。

**总结:** 这篇论文提出的TEM模型虽然没有直接解决单元格border的显式预测问题，但其像素级区域识别的思路以及多任务学习的框架，可以为LORE的改进提供一些间接的启发。如果能够将用户提供的border标注信息转化为像素级的边框掩码，那么TEM的思路可以作为一种潜在的实现方式。然而，相比TableBorderNet直接将边框作为语义分割目标，TEM在这方面显得不够直接。




## Borderless Tables Detection with Deep Learning and OpenCV

**文章链接:** https://medium.com/data-science/borderless-tables-detection-with-deep-learning-and-opencv-ebf568580fe2

**文章概述:** 这篇文章主要介绍了如何使用深度学习和OpenCV来检测无边框表格，并将半结构化数据转换为机器可读的文本。它侧重于使用目标检测方法来识别表格区域。

**模型架构:**
文章没有提出具体的模型名称，而是讨论了基于深度学习的目标检测方法，例如Faster R-CNN, SSD, YOLO等。它将无边框表格的检测视为一个目标检测任务，通过训练模型来识别图像中的表格区域。

**如何预测单元格的border属性:**
这篇文章的重点是“无边框表格检测”，这意味着它主要关注的是识别表格的整体区域，而不是预测每个单元格的border是否存在。它通过边界框（bounding box）来定位表格，而不是像素级别的边框预测。因此，它无法直接提供每个单元格的border信息。

**如何表示单元格之间的逻辑关系:**
文章没有详细说明如何表示单元格之间的逻辑关系。它主要关注的是表格的检测，对于表格内部的结构识别（如单元格划分、合并单元格识别）需要后续的步骤或不同的模型来完成。

**损失函数设计:**
作为目标检测任务，通常会使用目标检测模型中常用的损失函数，例如分类损失（用于判断是否是表格）和回归损失（用于预测表格的边界框坐标）。文章中没有给出具体的损失函数细节。

**开源代码仓库:**
文章中提到了TF2 Object Detection API，并提供了相关的安装和数据准备步骤，但没有直接提供一个完整的开源代码仓库链接，而是暗示读者可以从TensorFlow的Model Garden下载预训练模型。

**与LORE的借鉴点:**
1.  **无边框表格的挑战：** 这篇文章强调了无边框表格检测的挑战性，这与LORE在无线表格上的局限性有共通之处。虽然方法不同，但对无边框表格的关注点可以提醒LORE在处理这类表格时，可能需要更强的视觉特征提取能力。
2.  **目标检测的思路：** 尽管这篇文章没有直接解决border预测，但如果LORE需要更精确地识别表格的物理边界，可以考虑引入目标检测的思想来预测表格的整体边界框，或者将单元格的边界线视为目标进行检测。

**总结:** 这篇文章主要关注无边框表格的检测，而不是单元格border的精细预测。它采用目标检测的思路来识别表格区域，这与用户希望改进LORE对每个单元格border存在性预测的需求不完全匹配。虽然它强调了无边框表格的挑战，但对于解决用户提出的具体问题（预测每个单元格的border是否存在）的直接借鉴意义有限。TableBorderNet和SEMv3在这方面提供了更直接的解决方案。




## 对比分析与LORE改进方案

### 1. LORE模型回顾

《LORE: Logical Location Regression Network for Table Structure Recognition》论文提出了一种基于逻辑位置回归网络的表格结构识别方法。LORE的核心思想是将表格结构识别任务分解为两个子任务：单元格的物理位置回归和单元格的逻辑位置回归。它通过预测每个单元格的边界框（物理位置）以及其在表格中的行/列索引（逻辑位置）来还原表格结构。LORE的优点在于其能够处理复杂的合并单元格，并通过回归的方式获得精确的单元格物理和逻辑坐标。然而，正如用户所指出的，LORE在预测每个单元格的border是否存在方面存在局限性。它主要关注的是单元格的整体区域和逻辑关系，而不是构成这些单元格的每条边是否可见。这意味着LORE无法直接输出用户数据中“0表示border不可见，1表示border可见”的细粒度边框信息。

### 2. TableBorderNet分析

TableBorderNet是一个专门用于表格边框提取的语义分割框架。它将表格边框提取视为一个像素级别的二分类问题，直接预测图像中每个像素是否属于表格边框。其主要创新点包括：

*   **语义分割方法：** TableBorderNet通过一个编码器-解码器结构（基于FCN和ResNet）来学习图像的像素级特征，并输出一个与输入图像相同大小的概率图，表示每个像素是边框的概率。这种方法能够精确地识别边框的形状和位置，非常适合用户对“每个单元格的border是否存在”的需求，因为这本质上就是对每个像素进行边框/非边框的二分类。
*   **SliceConv增强模块：** 该模块通过行和列的逐片卷积和特征聚合来捕获表格边框的长期空间规律性，即使在边框存在不连续的情况下也能增强网络表达表格特征的能力。这对于处理无线表格或边框不完整的表格至关重要。
*   **TopoLoss（拓扑感知损失）：** TableBorderNet引入了拓扑感知损失，它不仅关注像素级别的准确性（通过BCE Loss），还通过计算预测与真实值之间的“差异图”来惩罚拓扑缺陷。这意味着模型在训练过程中会更加关注边框的连通性和整体结构的一致性，从而更好地还原表格的完整边框。

TableBorderNet的输出是像素级别的边框掩码，这与用户的数据标注方式（0表示border不可见，1表示border可见）高度契合。通过对每个单元格的四条边进行判断，可以从这个边框掩码中提取出所需的border信息。

### 3. SEMv3分析

SEMv3是一个快速且鲁棒的表格分离线检测方法，遵循“split-and-merge”范式。它将表格结构识别分解为分离线检测和网格合并两个阶段：

*   **Keypoint Offset Regression (KOR) 模块：** 在分离阶段，SEMv3引入KOR模块，通过回归关键点相对于其提议的偏移量来有效检测表格分离线。这种方法避免了复杂的mask-to-line后处理，并且通过预测偏移量而不是绝对坐标，降低了回归难度。这些分离线可以被视为表格的水平和垂直边框。
*   **“Split-and-Merge”范式：** SEMv3在检测到分离线后，会通过定义“merge actions”来合并过分割的表格网格，从而形成最终的单元格结构。这表明SEMv3能够处理单元格的合并情况，并在此基础上构建逻辑关系。

SEMv3通过检测分离线来识别表格的边框。虽然它不直接输出像素级的边框掩码，但其检测到的分离线可以用来判断单元格的边框是否存在。例如，如果一个单元格的某个方向（上、下、左、右）存在检测到的分离线，则可以认为该方向的border是可见的（1），否则是不可见的（0）。

### 4. 改进LORE模型的方案

基于对TableBorderNet和SEMv3的分析，我们可以为LORE模型在预测单元格border属性方面提出以下改进方案。考虑到用户的数据标注包含了每个单元格的border信息（0表示border不可见，1表示border可见），以下方案将侧重于如何利用这些标注数据。

**核心思路：** 在LORE现有的物理位置和逻辑位置回归的基础上，增加一个专门用于预测单元格border属性的模块。这个模块可以采用分类或回归的方式，输出每个单元格四条边（上、下、左、右）的可见性（0或1）。

**具体改进方案：**

#### 方案一：基于语义分割的边框预测（借鉴TableBorderNet）

这是最直接且与用户数据标注最契合的方案。将单元格border属性预测转化为一个像素级别的语义分割任务。

1.  **数据准备：**
    *   将原始图像作为输入。
    *   根据用户提供的“每个单元格的border信息（0表示border不可见，1表示border可见）”以及LORE预测的单元格物理位置，生成像素级别的边框真值掩码图。例如，对于每个单元格，如果其上边框可见，则将该单元格上边缘的像素标记为1，否则标记为0。可以为每个方向（上、下、左、右）生成单独的掩码图，或者生成一个包含所有边框信息的单一掩码图。

2.  **模型架构修改：**
    *   在LORE的特征提取器（例如，其用于物理位置回归的骨干网络）之后，增加一个并行的语义分割分支。这个分支可以是一个轻量级的编码器-解码器结构（例如，类似U-Net的结构），用于从视觉特征中预测边框掩码图。
    *   **输入：** 原始图像或LORE骨干网络提取的视觉特征。
    *   **输出：** 一个与输入图像分辨率相同的二值掩码图，其中像素值为1表示该位置存在边框，0表示不存在。或者，可以输出四个独立的掩码图，分别对应单元格的上、下、左、右边框。

3.  **损失函数设计：**
    *   **LORE原有损失：** 保留LORE原有的物理位置回归损失和逻辑位置回归损失。
    *   **边框预测损失：** 引入二元交叉熵损失（Binary Cross-Entropy Loss）来训练语义分割分支，衡量预测边框掩码与真值掩码之间的差异。可以考虑借鉴TableBorderNet的TopoLoss思想，在BCE损失的基础上增加一个拓扑感知损失，以确保预测边框的连通性和完整性，尤其是在无线表格或边框不完整的情况下。
    *   **总损失：** L_total = L_LORE + λ * L_border_prediction，其中λ是平衡LORE原有任务和边框预测任务的权重。

4.  **优势：**
    *   直接利用像素级标注数据，能够精确还原每个单元格的border是否存在。
    *   语义分割方法在处理图像细节方面表现出色，有助于识别模糊或不完整的边框。
    *   可以与LORE的物理位置预测相结合，确保边框与单元格的几何位置一致。

5.  **挑战：**
    *   需要高质量的像素级边框真值掩码数据。如果现有标注只是0/1的可见性，需要将其转换为像素级掩码。
    *   增加语义分割分支会增加模型的复杂度和计算量。

#### 方案二：基于单元格属性分类/回归的边框预测（结合LORE和SEMv3思路）

这个方案更侧重于在单元格级别预测边框属性，而不是像素级别。

1.  **数据准备：**
    *   对于LORE预测的每个单元格，提取其对应的视觉特征（例如，从LORE骨干网络中提取的单元格区域特征）。
    *   根据用户提供的标注，为每个单元格的四条边（上、下、左、右）生成一个0/1的标签，表示该边框是否存在。

2.  **模型架构修改：**
    *   在LORE的单元格特征提取部分之后，增加一个并行的分类/回归头。这个头可以是一个简单的全连接网络。
    *   **输入：** 每个单元格的视觉特征（例如，通过RoI Align/Pooling从LORE的特征图中提取）。
    *   **输出：** 对于每个单元格，输出一个4维向量，每个维度对应上、下、左、右边框的可见性概率（0到1之间的值）。

3.  **损失函数设计：**
    *   **LORE原有损失：** 保留LORE原有的物理位置回归损失和逻辑位置回归损失。
    *   **边框预测损失：** 引入二元交叉熵损失（Binary Cross-Entropy Loss）来训练边框可见性分类任务。如果需要更精确的边框位置，可以增加回归损失来预测边框的偏移量（借鉴SEMv3的KOR思想）。
    *   **总损失：** L_total = L_LORE + λ * L_cell_border_classification (+ μ * L_border_offset_regression)。

4.  **优势：**
    *   相对方案一，模型复杂度可能较低，计算量较小。
    *   直接在单元格级别进行预测，与LORE的单元格中心思想更贴近。

5.  **挑战：**
    *   可能无法像像素级语义分割那样精确地还原边框的细节和形状。
    *   对于无线表格，如果单元格本身没有明确的视觉边界，提取其视觉特征并预测边框可能会更具挑战性。

#### 方案三：结合两种方案的混合方法

为了获得最佳效果，可以考虑结合方案一和方案二的优点。

1.  **多任务学习：** LORE模型可以同时拥有三个输出分支：
    *   **物理位置回归分支：** 预测单元格的边界框。
    *   **逻辑位置回归分支：** 预测单元格的行/列索引和合并信息。
    *   **边框语义分割分支：** 预测像素级别的边框掩码（借鉴TableBorderNet）。
    *   **单元格边框属性分类分支：** 在单元格级别预测每个单元格四条边的可见性（0/1），作为辅助任务或精炼任务。

2.  **损失函数：** 结合所有分支的损失，并使用适当的权重进行平衡。

3.  **优势：**
    *   能够同时获得精确的单元格物理/逻辑结构和细粒度的边框信息。
    *   多任务学习有助于模型更好地理解表格的整体结构和局部细节。

4.  **挑战：**
    *   模型复杂度最高，训练难度最大。
    *   需要精心设计损失函数的权重，以平衡不同任务的重要性。

**总结与建议：**

考虑到用户明确指出“数据标注是包含了每个单元格的border信息的（0表示border不可见，1表示border可见）”，并且希望“完全还原每个单元格的boder是否存在”，**方案一（基于语义分割的边框预测）是最推荐的改进方向。** 这种方法能够直接利用像素级别的边框真值，并通过语义分割模型输出精确的边框掩码，从而直接满足用户的需求。同时，可以借鉴TableBorderNet的TopoLoss来增强边框的拓扑完整性。

如果现有标注只是单元格级别的0/1可见性，那么在实施方案一之前，需要将这些标注转换为像素级别的边框掩码。这可能需要一些额外的预处理步骤，例如，根据单元格的物理位置和0/1可见性，在图像上绘制出对应的边框像素。如果无法生成像素级真值，那么方案二（基于单元格属性分类/回归）可能是一个更实际的选择，但其输出的边框细节可能不如语义分割精确。

无论选择哪种方案，都建议在LORE现有模型的基础上进行增量式改进，逐步添加和优化边框预测模块，并通过实验验证其效果。同时，可以参考TableBorderNet和SEMv3的开源代码实现，了解其在模型设计和损失函数方面的具体细节，从而更好地指导LORE的改进。

