from __future__ import absolute_import
from __future__ import division
from __future__ import print_function

from typing import Optional, Dict, Any, Type
import logging

from .sample.ctdet import CTDetDataset
from .sample.table_ctdet import TableLabelMeCTDetDataset

from .dataset.table import Table
from .dataset.table_small import Table as Table_small
from .dataset.table_mid import Table as Table_mid
from .dataset.table_labelmev2 import Table as Table_labelmev2


dataset_factory = {
  'table':Table,
  'table_mid':Table_mid,
  'table_small':Table_small,
  'table_labelmev2':Table_labelmev2
}

_sample_factory = {
  'ctdet': CTDetDataset,
  'ctdet_mid': CTDetDataset,
  'ctdet_small': CTDetDataset,
  'table_ctdet': TableLabelMeCTDetDataset,
  'table_ctdet_mid': TableLabelMeCTDetDataset,
  'table_ctdet_small': TableLabelMeCTDetDataset
}

def _create_tablelabelme_dataset(task: str) -> Type:
    """
    创建TableLabelMe数据集类（步骤5.5扩展版本）。

    Args:
        task (str): 任务类型，如'ctdet_mid'

    Returns:
        Type: TableLabelMe数据集类

    Note:
        这是步骤5.5的扩展函数，用于创建TableLabelMe数据集的组合类。
        使用多重继承机制，组合Table_labelmev2和对应的采样类。

        步骤5.5新增功能：
        - 对于ctdet系列任务，优先使用TableLabelMeCTDetDataset
        - 保持向后兼容性，支持所有现有任务类型
    """
    # 映射到TableLabelMe专用的采样类
    if task.startswith('ctdet'):
        sample_class = TableLabelMeCTDetDataset
    else:
        sample_class = _sample_factory.get(task, TableLabelMeCTDetDataset)

    class TableLabelMeDataset(Table_labelmev2, sample_class):
        """
        TableLabelMe数据集组合类（步骤5.4）。

        通过多重继承组合TableLabelMe数据集基类和采样类，
        提供完整的TableLabelMe数据集功能。
        """
        pass

    # 设置类名以便调试
    TableLabelMeDataset.__name__ = f'TableLabelMe_{task}_Dataset'
    TableLabelMeDataset.__qualname__ = f'TableLabelMe_{task}_Dataset'

    return TableLabelMeDataset

def get_dataset(dataset: str, task: str, config: Optional[Dict[str, Any]] = None) -> Type:
    """
    数据集工厂函数（步骤5.4扩展版本）。

    Args:
        dataset (str): 数据集名称，如'table_mid'
        task (str): 任务类型，如'ctdet_mid'
        config (Optional[Dict[str, Any]]): 可选的配置对象（迭代4新增）

    Returns:
        Type: 数据集类

    Note:
        这是步骤5.4扩展的工厂函数，支持：
        1. 智能模式检测：根据config参数自动选择数据集类型
        2. TableLabelMe集成：支持完整的TableLabelMe数据集创建
        3. 向后兼容性：保持现有COCO格式调用完全不变
        4. 参数验证：提供详细的错误提示
    """
    try:
        # 步骤1：检查config参数，实现智能模式检测
        if config is not None:
            dataset_mode = config.get('dataset_mode', 'COCO')

            if dataset_mode == 'TableLabelMe':
                # TableLabelMe模式：使用完整的TableLabelMe数据集
                logging.info(f"[工厂函数] 检测到TableLabelMe模式，创建TableLabelMe数据集 (task: {task})")
                return _create_tablelabelme_dataset(task)

            elif dataset_mode == 'COCO':
                # COCO模式：使用原有逻辑
                logging.info(f"[工厂函数] 检测到COCO模式，使用原有数据集逻辑 (dataset: {dataset}, task: {task})")

            else:
                # 未知模式：记录警告但继续使用COCO逻辑
                logging.warning(f"[工厂函数] 未知的数据集模式: {dataset_mode}，回退到COCO模式")

        # 步骤2：使用原有COCO逻辑（向后兼容性保证）
        if dataset not in dataset_factory:
            raise ValueError(f"不支持的数据集: {dataset}. 支持的数据集: {list(dataset_factory.keys())}")

        if task not in _sample_factory:
            raise ValueError(f"不支持的任务类型: {task}. 支持的类型: {list(_sample_factory.keys())}")

        # 创建COCO格式数据集组合类
        dataset_class = dataset_factory[dataset]
        sample_class = _sample_factory[task]

        class Dataset(dataset_class, sample_class):
            """
            COCO格式数据集组合类（原有逻辑）。

            通过多重继承组合数据集基类和采样类，
            保持与现有训练流程的完全兼容性。
            """
            pass

        # 设置类名以便调试
        Dataset.__name__ = f'{dataset}_{task}_Dataset'
        Dataset.__qualname__ = f'{dataset}_{task}_Dataset'

        logging.info(f"[工厂函数] 成功创建COCO数据集类: {Dataset.__name__}")
        return Dataset

    except Exception as e:
        # fail-fast原则：让错误尽早、清晰地暴露
        error_msg = f"数据集创建失败 - dataset: {dataset}, task: {task}, config: {config is not None}, 错误: {str(e)}"
        logging.error(f"[工厂函数] {error_msg}")
        raise RuntimeError(error_msg) from e
  
