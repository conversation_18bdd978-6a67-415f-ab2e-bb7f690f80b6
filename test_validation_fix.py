#!/usr/bin/env python3
"""
验证修复测试脚本

该脚本专门测试验证阶段的数据加载，
使用调试collate函数来精确定位整数溢出问题。

使用方法:
python test_validation_fix.py

作者: AI Assistant
日期: 2025-07-23
"""

import sys
import os
import torch

# 添加项目路径
sys.path.append('src')
sys.path.append('src/lib')

def test_validation_data_loading():
    """测试验证数据加载"""
    print("🧪 开始验证数据加载测试")
    
    try:
        # 导入必要的模块
        from opts import opts
        from datasets.dataset.ctdet import CTDetDataset as Dataset
        from debug_collate import debug_collate_fn
        
        # 创建选项（模拟命令行参数）
        opt = opts().init()
        opt.task = 'ctdet_mid'
        opt.dataset = 'table'
        opt.dataset_name = 'TableLabelMe'
        opt.data_config = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/lib/configs/my_dataset_configs.py'
        opt.config_name = 'tableme_full'
        opt.exp_id = 'test_validation'
        opt.batch_size = 1
        opt.num_workers = 0
        opt.gpus = '0'
        
        print(f"✅ 选项配置完成")
        
        # 创建验证数据集
        print(f"🔧 创建验证数据集...")
        val_dataset = Dataset(opt, 'val')
        print(f"✅ 验证数据集创建成功: {len(val_dataset)}个样本")
        
        # 创建数据加载器（使用调试collate函数）
        print(f"🔧 创建验证数据加载器...")
        val_loader = torch.utils.data.DataLoader(
            val_dataset,
            batch_size=1,
            shuffle=False,
            num_workers=0,
            pin_memory=False,
            collate_fn=debug_collate_fn
        )
        print(f"✅ 验证数据加载器创建成功")
        
        # 测试前几个样本
        print(f"🧪 开始测试前5个验证样本...")
        
        for i, batch in enumerate(val_loader):
            print(f"\n[测试] 样本 {i+1}:")
            print(f"[测试] 批次字段: {list(batch.keys())}")
            
            # 检查关键字段
            for field_name, tensor in batch.items():
                if isinstance(tensor, torch.Tensor):
                    print(f"[测试] {field_name}: shape={tensor.shape}, dtype={tensor.dtype}")
                    
                    # 检查整数类型的张量
                    if tensor.dtype in [torch.int32, torch.int64, torch.long]:
                        print(f"[测试] {field_name} 数值范围: [{tensor.min().item()}, {tensor.max().item()}]")
                    elif tensor.dtype in [torch.float32, torch.float64]:
                        print(f"[测试] {field_name} 数值范围: [{tensor.min().item():.2f}, {tensor.max().item():.2f}]")
                else:
                    print(f"[测试] {field_name}: type={type(tensor)}")
            
            # 只测试前5个样本
            if i >= 4:
                break
        
        print(f"\n✅ 验证数据加载测试成功！前5个样本都正常加载")
        return True
        
    except Exception as e:
        print(f"\n❌ 验证数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_problematic_sample():
    """测试特定的问题样本"""
    print(f"\n🧪 测试特定问题样本")
    
    try:
        # 导入必要的模块
        from opts import opts
        from datasets.dataset.ctdet import CTDetDataset as Dataset
        
        # 创建选项
        opt = opts().init()
        opt.task = 'ctdet_mid'
        opt.dataset = 'table'
        opt.dataset_name = 'TableLabelMe'
        opt.data_config = '/aipdf-mlp/lanx/workspace/projects/LORE-adapt/src/lib/configs/my_dataset_configs.py'
        opt.config_name = 'tableme_full'
        opt.exp_id = 'test_single'
        
        # 创建验证数据集
        val_dataset = Dataset(opt, 'val')
        
        # 测试索引5的样本（从日志中看到这个样本有问题）
        print(f"🔧 测试索引5的样本...")
        try:
            sample = val_dataset[5]
            print(f"✅ 样本5加载成功")
            
            # 检查样本数据
            for field_name, data in sample.items():
                if hasattr(data, 'shape'):
                    print(f"[样本5] {field_name}: shape={data.shape}, dtype={data.dtype}")
                    
                    if hasattr(data, 'min') and hasattr(data, 'max'):
                        print(f"[样本5] {field_name} 范围: [{data.min()}, {data.max()}]")
                else:
                    print(f"[样本5] {field_name}: type={type(data)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 样本5加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ 单样本测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始验证修复完整测试")
    print("=" * 60)
    
    # 运行测试
    test_results = []
    
    # 测试1: 验证数据加载
    test_results.append(test_validation_data_loading())
    
    # 测试2: 特定问题样本
    test_results.append(test_single_problematic_sample())
    
    # 生成测试报告
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    test_names = [
        "验证数据加载测试",
        "特定问题样本测试"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(test_results)} 测试通过")
    
    if all(test_results):
        print("🎉 所有测试通过！验证修复成功！")
        print("💡 现在可以尝试运行完整的训练命令")
        return 0
    else:
        print("⚠️  部分测试失败，需要进一步调试")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
