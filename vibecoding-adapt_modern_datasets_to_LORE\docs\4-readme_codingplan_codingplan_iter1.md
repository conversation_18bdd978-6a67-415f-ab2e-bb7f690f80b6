# LORE-TSR项目TableLabelMe数据格式支持 - 迭代一MVP版本开发步骤

## 项目概述

### 开发目标
为LORE-TSR项目增量支持TableLabelMe数据格式，实现基础格式解析器（MVP版本），在保持与原有COCO格式完全兼容的前提下，为后续迭代奠定架构基础。

### 核心原则
- **小步快跑**：将迭代1分解为6个可独立验证的小步骤
- **持续验证**：每个步骤都确保项目保持可运行状态
- **无侵害性**：对原项目核心逻辑零修改
- **接口一致性**：Dataset.__getitem__返回结构与COCO格式完全兼容

### 迭代1范围
- 创建TableLabelMe格式解析器类 `TableLabelMeParser`
- 实现bbox.p1-p4到segmentation的坐标转换
- 实现lloc字段到logic_axis的结构转换
- 处理cell_ind和文件名关联生成全局唯一ID
- 创建TableLabelMe数据集类并集成到工厂模式

## 开发步骤详解

### 步骤1：创建解析器基础架构

**步骤标题**：迭代1步骤1.1 - 创建解析器模块基础架构

**当前迭代**：迭代1 - 基础格式解析器（MVP）

**影响文件**：
- `src/lib/datasets/parsers/__init__.py` [新建]
- `src/lib/datasets/parsers/base_parser.py` [新建]

**具体操作**：
1. 创建`src/lib/datasets/parsers/`目录
2. 创建解析器包初始化文件，提供统一的解析器接口导入
3. 创建解析器基类，定义统一的解析接口和通用方法

**核心代码模板**：
```python
# parsers/__init__.py
from .base_parser import BaseParser

__all__ = ['BaseParser']

# base_parser.py
import json
import hashlib
from abc import ABC, abstractmethod

class BaseParser(ABC):
    """解析器基类，为后续支持其他数据格式提供统一接口"""
    
    @abstractmethod
    def parse_file(self, json_path, image_path):
        """解析单个标注文件，返回标准化数据字典"""
        pass
    
    def generate_image_id(self, file_path):
        """基于文件路径生成稳定的图像ID"""
        return int(hashlib.md5(file_path.encode()).hexdigest()[:8], 16)
```

**验证方法**：
```bash
cd LORE-TSR/src
python -c "from lib.datasets.parsers import BaseParser; print('解析器基础架构创建成功')"
```

**验收标准**：
- 解析器模块可以正确导入
- BaseParser基类定义完整
- 无语法错误和导入错误

---

### 步骤2：实现TableLabelMe解析器核心功能

**步骤标题**：迭代1步骤1.2 - 实现TableLabelMe格式解析器

**当前迭代**：迭代1 - 基础格式解析器（MVP）

**影响文件**：
- `src/lib/datasets/parsers/tablelabelme_parser.py` [新建]
- `src/lib/datasets/parsers/__init__.py` [修改]

**具体操作**：
1. 创建TableLabelMe格式的具体解析器实现
2. 实现坐标转换算法：将四个角点(p1-p4)转换为一维segmentation数组
3. 实现逻辑结构转换：将start_row/end_row/start_col/end_col组合为logic_axis
4. 实现ID生成策略和质量筛选功能

**核心功能实现**：
- `convert_bbox_to_segmentation()`: bbox.p1-p4 → segmentation数组
- `convert_lloc_to_logic_axis()`: lloc → logic_axis数组  
- `calculate_area()`: 根据segmentation计算面积
- `filter_by_quality()`: 根据quality字段筛选合格标注

**验证方法**：
```bash
# 创建测试JSON文件并验证解析功能
cd LORE-TSR/src
python -c "
from lib.datasets.parsers import TableLabelMeParser
parser = TableLabelMeParser()
print('TableLabelMe解析器创建成功')
"
```

**验收标准**：
- 解析器可以正确处理TableLabelMe JSON格式
- 坐标转换数学正确性验证通过
- 异常处理：JSON格式错误时返回None而非抛出异常

---

### 步骤3：创建TableLabelMe数据集类

**步骤标题**：迭代1步骤1.3 - 创建TableLabelMe数据集类

**当前迭代**：迭代1 - 基础格式解析器（MVP）

**影响文件**：
- `src/lib/datasets/dataset/table_labelmev2.py` [新建]

**具体操作**：
1. 创建TableLabelMe格式的数据集类，继承现有Table类
2. 重写数据加载逻辑，集成TableLabelMe解析器
3. 实现MVP版本的文件索引构建（使用固定测试数据）
4. 确保__getitem__返回与COCO格式兼容的数据结构

**核心方法实现**：
- `__init__()`: 初始化数据集，设置基本参数
- `_load_annotations()`: 加载并解析TableLabelMe标注文件
- `_build_file_index()`: 构建文件索引（MVP版本使用占位实现）
- `__len__()`: 返回数据集大小

**验证方法**：
```bash
cd LORE-TSR/src
python -c "
from lib.datasets.dataset.table_labelmev2 import Table as TableLabelMe
print('TableLabelMe数据集类创建成功')
"
```

**验收标准**：
- 数据集类可以正确初始化
- 继承关系正确，与现有Table类兼容
- __getitem__返回结构与COCO格式一致

---

### 步骤4：集成到数据集工厂

**步骤标题**：迭代1步骤1.4 - 集成TableLabelMe到数据集工厂

**当前迭代**：迭代1 - 基础格式解析器（MVP）

**影响文件**：
- `src/lib/datasets/dataset_factory.py` [修改]

**具体操作**：
1. 在dataset_factory.py中添加TableLabelMe数据集映射
2. 扩展get_dataset函数支持table_labelmev2类型
3. 确保向后兼容，不影响现有COCO格式数据集

**核心修改**：
```python
# 在现有代码基础上添加
from .dataset.table_labelmev2 import Table as Table_labelmev2

dataset_factory = {
  'table': Table,
  'table_mid': Table_mid, 
  'table_small': Table_small,
  'table_labelmev2': Table_labelmev2  # 新增映射
}
```

**验证方法**：
```bash
cd LORE-TSR/src
python -c "
from lib.datasets.dataset_factory import get_dataset
dataset_class = get_dataset('table_labelmev2', 'ctdet')
print('数据集工厂集成成功')
"
```

**验收标准**：
- 工厂可以正确创建TableLabelMe数据集
- 现有COCO格式数据集功能不受影响
- 数据集类型映射正确

---

### 步骤5：扩展配置参数支持

**步骤标题**：迭代1步骤1.5 - 扩展配置参数支持

**当前迭代**：迭代1 - 基础格式解析器（MVP）

**影响文件**：
- `src/lib/opts.py` [修改]

**具体操作**：
1. 在opts.py中添加data_config参数定义
2. 为后续迭代的外部配置文件支持预留接口
3. 确保参数兼容性，不影响现有参数体系

**核心修改**：
```python
# 在参数定义部分添加
self.parser.add_argument('--data_config', default='',
                         help='path to external dataset configuration file')
```

**验证方法**：
```bash
cd LORE-TSR/src
python main.py ctdet --help | grep data_config
echo "配置参数扩展成功"
```

**验收标准**：
- 新参数可以正确解析
- 现有参数功能不受影响
- 帮助信息显示正确

---

### 步骤6：端到端验证和测试

**步骤标题**：迭代1步骤1.6 - 端到端验证和集成测试

**当前迭代**：迭代1 - 基础格式解析器（MVP）

**影响文件**：
- `test_tablelabelme_integration.py` [新建，测试文件]

**具体操作**：
1. 创建简单的集成测试脚本
2. 验证整个TableLabelMe解析流程
3. 确保与现有COCO格式训练流程兼容
4. 生成测试报告和验证结果

**验证脚本示例**：
```python
#!/usr/bin/env python3
"""TableLabelMe格式支持集成测试脚本"""

def test_tablelabelme_integration():
    """测试TableLabelMe格式的完整集成"""
    # 测试解析器功能
    # 测试数据集创建
    # 测试工厂模式
    # 测试配置参数
    print("所有测试通过，TableLabelMe格式支持集成成功")

if __name__ == "__main__":
    test_tablelabelme_integration()
```

**验证方法**：
```bash
cd LORE-TSR/src
python test_tablelabelme_integration.py
```

**验收标准**：
- 所有组件集成测试通过
- TableLabelMe格式解析功能正常
- 不影响现有COCO格式功能
- 项目保持可运行状态

## 技术约束和注意事项

### 核心约束
- **算法一致性**：确保格式转换的数学正确性
- **无侵害性**：对原项目核心逻辑零修改
- **接口一致性**：Dataset.__getitem__返回结构保持兼容
- **独立性**：解析器不依赖现有LORE-TSR模块

### 异常处理策略
- JSON格式错误时返回None而非抛出异常
- 文件缺失时记录日志并跳过
- 单个文件异常不影响整体处理流程

### 后续迭代占位
- 迭代2：目录扫描功能在`_build_file_index`方法中占位
- 迭代3：质量筛选功能在`filter_by_quality`方法中基础实现
- 迭代4：配置系统通过`data_config`参数占位

## 文件结构变化

### 新增文件
```
LORE-TSR/src/lib/datasets/
├── parsers/                             # [新增] 格式解析器模块
│   ├── __init__.py                      # [新增] 解析器包初始化
│   ├── base_parser.py                   # [新增] 解析器基类
│   └── tablelabelme_parser.py           # [新增] TableLabelMe格式解析器
└── dataset/
    └── table_labelmev2.py               # [新增] TableLabelMe格式数据集
```

### 修改文件
- `src/lib/datasets/dataset_factory.py` - 添加TableLabelMe映射
- `src/lib/opts.py` - 添加data_config参数支持

### 测试文件
- `test_tablelabelme_integration.py` - 集成测试脚本

## 验收标准总结

### 功能完整性
- TableLabelMe格式解析器功能完整
- 坐标转换和逻辑转换正确
- 数据集类正确集成到工厂模式

### 兼容性保证
- 原有COCO格式功能完全不受影响
- Dataset.__getitem__返回结构兼容
- 配置参数向后兼容

### 代码质量
- 每个模块代码量控制在500行以内
- 异常处理机制完善
- 代码结构清晰，易于扩展

---

**文档版本**：v1.0  
**创建日期**：2025年1月21日  
**适用迭代**：迭代1 - 基础格式解析器（MVP）  
**后续迭代**：为迭代2-6预留架构扩展接口
