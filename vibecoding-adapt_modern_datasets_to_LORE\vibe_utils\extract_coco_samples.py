#!/usr/bin/env python3
"""
COCO格式样本提取脚本
专门用于从大型COCO格式数据集中提取指定数量的样本，保持原始格式
"""

import json
import os
import sys
import argparse
from collections import defaultdict


def extract_coco_samples(input_file: str, output_file: str, num_samples: int = 2):
    """
    从COCO格式数据集中提取指定数量的样本
    
    Args:
        input_file: 输入的COCO格式JSON文件路径
        output_file: 输出的样本文件路径
        num_samples: 要提取的样本数量
    """
    print(f"正在加载文件: {input_file}")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print("文件加载成功!")
    except Exception as e:
        print(f"加载文件失败: {e}")
        return False
    
    # 获取原始数据
    images = data.get('images', [])
    annotations = data.get('annotations', [])
    categories = data.get('categories', [])
    data_type = data.get('type', 'instances')
    
    print(f"原始数据统计:")
    print(f"  图像数量: {len(images)}")
    print(f"  标注数量: {len(annotations)}")
    print(f"  类别数量: {len(categories)}")
    
    # 选择前num_samples个图像
    sample_images = images[:num_samples]
    sample_image_ids = {img['id'] for img in sample_images}
    
    print(f"选择的图像ID: {list(sample_image_ids)}")
    
    # 找到这些图像对应的所有标注
    sample_annotations = []
    for ann in annotations:
        if ann.get('image_id') in sample_image_ids:
            sample_annotations.append(ann)
    
    print(f"找到 {len(sample_annotations)} 个相关标注")
    
    # 构建COCO格式的样本数据
    coco_samples = {
        'images': sample_images,
        'annotations': sample_annotations,
        'categories': categories,  # 保留所有类别信息
        'type': data_type
    }
    
    # 保存样本数据
    try:
        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(coco_samples, f, ensure_ascii=False, indent=2)
        
        print(f"样本数据已保存到: {output_file}")
        print(f"样本统计:")
        print(f"  图像数量: {len(coco_samples['images'])}")
        print(f"  标注数量: {len(coco_samples['annotations'])}")
        print(f"  类别数量: {len(coco_samples['categories'])}")
        
        return True
        
    except Exception as e:
        print(f"保存样本数据失败: {e}")
        return False


def analyze_sample_details(sample_file: str):
    """
    分析样本文件的详细信息
    
    Args:
        sample_file: 样本文件路径
    """
    try:
        with open(sample_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"读取样本文件失败: {e}")
        return
    
    print("\n" + "="*60)
    print("样本详细分析")
    print("="*60)
    
    images = data.get('images', [])
    annotations = data.get('annotations', [])
    categories = data.get('categories', [])
    
    # 创建图像ID到标注的映射
    image_to_annotations = defaultdict(list)
    for ann in annotations:
        if 'image_id' in ann:
            image_to_annotations[ann['image_id']].append(ann)
    
    # 分析每个图像
    for i, image in enumerate(images):
        print(f"\n图像 {i+1}:")
        print(f"  ID: {image.get('id')}")
        print(f"  文件名: {image.get('file_name')}")
        print(f"  尺寸: {image.get('width')} x {image.get('height')}")
        
        image_id = image.get('id')
        image_annotations = image_to_annotations.get(image_id, [])
        print(f"  标注数量: {len(image_annotations)}")
        
        if image_annotations:
            # 分析标注的类别分布
            category_counts = defaultdict(int)
            for ann in image_annotations:
                category_counts[ann.get('category_id')] += 1
            
            print(f"  类别分布:")
            for cat_id, count in category_counts.items():
                print(f"    类别ID {cat_id}: {count} 个标注")
            
            # 显示第一个标注的详细信息
            first_ann = image_annotations[0]
            print(f"  第一个标注示例:")
            print(f"    标注ID: {first_ann.get('id')}")
            print(f"    类别ID: {first_ann.get('category_id')}")
            print(f"    边界框: {first_ann.get('bbox')}")
            print(f"    面积: {first_ann.get('area')}")
            if 'segmentation' in first_ann:
                seg = first_ann['segmentation']
                if isinstance(seg, list) and len(seg) > 0:
                    print(f"    分割点数: {len(seg[0]) // 2} 个点")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='COCO格式样本提取工具')
    parser.add_argument('--input', required=True, help='输入的COCO格式JSON文件路径')
    parser.add_argument('--output', required=True, help='输出的样本文件路径')
    parser.add_argument('-n', '--num-samples', type=int, default=2, help='提取的样本数量')
    parser.add_argument('--analyze', action='store_true', help='分析生成的样本文件')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在 - {args.input}")
        sys.exit(1)
    
    # 提取样本
    success = extract_coco_samples(args.input, args.output, args.num_samples)
    
    if success and args.analyze:
        # 分析样本
        analyze_sample_details(args.output)


if __name__ == "__main__":
    main()
