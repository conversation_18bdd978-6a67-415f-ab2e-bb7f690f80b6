#!/usr/bin/env python3
"""
调试collate函数 - 精确定位整数溢出问题

该脚本创建一个自定义的collate函数，用于精确定位
在PyTorch DataLoader collate阶段导致整数溢出的具体字段。

使用方法:
1. 导入此模块
2. 使用debug_collate_fn替换默认的collate函数
3. 观察详细的调试输出

作者: AI Assistant  
日期: 2025-07-23
"""

import torch
import numpy as np
from typing import Any, Dict, List


def safe_tensor_creation(data, field_name: str, batch_idx: int = 0):
    """
    安全创建PyTorch张量，检测和处理整数溢出
    
    Args:
        data: 要转换的数据
        field_name: 字段名称（用于日志）
        batch_idx: 批次索引（用于日志）
        
    Returns:
        安全的PyTorch张量
    """
    try:
        # 检查数据类型
        if isinstance(data, np.ndarray):
            print(f"[调试] 字段 {field_name}[{batch_idx}]: numpy数组, shape={data.shape}, dtype={data.dtype}")
            
            # 检查整数类型的数组
            if np.issubdtype(data.dtype, np.integer):
                print(f"[调试] 整数数组 {field_name}: min={data.min()}, max={data.max()}")
                
                # 检查是否超出安全范围
                MAX_SAFE_INT = 2147483647
                MIN_SAFE_INT = -2147483648
                
                if data.max() > MAX_SAFE_INT or data.min() < MIN_SAFE_INT:
                    print(f"[警告] 🚨 字段 {field_name} 检测到整数溢出风险!")
                    print(f"[警告] 原始范围: [{data.min()}, {data.max()}]")
                    
                    # 截断到安全范围
                    data_safe = np.clip(data, MIN_SAFE_INT, MAX_SAFE_INT)
                    print(f"[修复] 截断后范围: [{data_safe.min()}, {data_safe.max()}]")
                    
                    # 转换为int32确保兼容性
                    if data.dtype != np.int32:
                        data_safe = data_safe.astype(np.int32)
                        print(f"[修复] 转换数据类型: {data.dtype} -> {data_safe.dtype}")
                    
                    return torch.from_numpy(data_safe)
                else:
                    print(f"[正常] 字段 {field_name} 整数范围安全")
            
            # 浮点数数组
            elif np.issubdtype(data.dtype, np.floating):
                print(f"[调试] 浮点数组 {field_name}: min={data.min():.2f}, max={data.max():.2f}")
                
                # 检查NaN和Inf
                if np.any(np.isnan(data)) or np.any(np.isinf(data)):
                    print(f"[警告] 字段 {field_name} 包含NaN或Inf值")
                    data = np.nan_to_num(data, nan=0.0, posinf=1e6, neginf=-1e6)
                    print(f"[修复] 已清理NaN/Inf值")
            
            return torch.from_numpy(data)
            
        # 处理标量值
        elif isinstance(data, (int, np.integer)):
            print(f"[调试] 标量整数 {field_name}[{batch_idx}]: {data}")
            
            if data > 2147483647 or data < -2147483648:
                print(f"[警告] 🚨 标量 {field_name} 整数溢出: {data}")
                data = max(-2147483648, min(2147483647, int(data)))
                print(f"[修复] 截断标量: {data}")
            
            return torch.tensor(data, dtype=torch.int32)
            
        # 处理浮点数
        elif isinstance(data, (float, np.floating)):
            print(f"[调试] 标量浮点 {field_name}[{batch_idx}]: {data}")
            return torch.tensor(data, dtype=torch.float32)
            
        # 处理列表
        elif isinstance(data, list):
            print(f"[调试] 列表 {field_name}[{batch_idx}]: 长度={len(data)}")
            
            # 检查列表中的元素类型
            if len(data) > 0:
                first_elem = data[0]
                if isinstance(first_elem, (int, np.integer)):
                    # 检查列表中的整数
                    max_val = max(data)
                    min_val = min(data)
                    print(f"[调试] 整数列表范围: [{min_val}, {max_val}]")
                    
                    if max_val > 2147483647 or min_val < -2147483648:
                        print(f"[警告] 🚨 列表 {field_name} 包含溢出整数")
                        data = [max(-2147483648, min(2147483647, int(x))) for x in data]
                        print(f"[修复] 已截断列表中的整数")
            
            return torch.tensor(data)
            
        else:
            print(f"[调试] 其他类型 {field_name}[{batch_idx}]: {type(data)}")
            return torch.tensor(data)
            
    except Exception as e:
        print(f"[错误] ❌ 字段 {field_name}[{batch_idx}] 张量创建失败: {e}")
        print(f"[错误] 数据类型: {type(data)}")
        if hasattr(data, 'shape'):
            print(f"[错误] 数据形状: {data.shape}")
        if hasattr(data, 'dtype'):
            print(f"[错误] 数据类型: {data.dtype}")
        
        # 返回安全的默认张量
        if isinstance(data, np.ndarray):
            return torch.zeros_like(torch.from_numpy(data.astype(np.float32)))
        else:
            return torch.tensor(0, dtype=torch.float32)


def debug_collate_fn(batch: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    调试版本的collate函数，用于精确定位整数溢出问题
    
    Args:
        batch: 批次数据列表
        
    Returns:
        整理后的批次数据
    """
    print(f"\n[调试] 🔍 开始collate调试，批次大小: {len(batch)}")
    
    if len(batch) == 0:
        return {}
    
    # 获取所有字段名
    field_names = list(batch[0].keys())
    print(f"[调试] 字段列表: {field_names}")
    
    result = {}
    
    for field_name in field_names:
        print(f"\n[调试] 处理字段: {field_name}")
        
        try:
            # 收集该字段的所有数据
            field_data = []
            for batch_idx, sample in enumerate(batch):
                if field_name in sample:
                    data = sample[field_name]
                    print(f"[调试] 样本 {batch_idx} 的 {field_name}: type={type(data)}")
                    
                    if hasattr(data, 'shape'):
                        print(f"[调试] 形状: {data.shape}")
                    if hasattr(data, 'dtype'):
                        print(f"[调试] 数据类型: {data.dtype}")
                    
                    # 使用安全张量创建
                    safe_tensor = safe_tensor_creation(data, field_name, batch_idx)
                    field_data.append(safe_tensor)
                else:
                    print(f"[警告] 样本 {batch_idx} 缺少字段 {field_name}")
            
            if field_data:
                # 尝试堆叠张量
                try:
                    if len(field_data) == 1:
                        result[field_name] = field_data[0].unsqueeze(0)
                    else:
                        result[field_name] = torch.stack(field_data)
                    
                    print(f"[成功] ✅ 字段 {field_name} collate成功: {result[field_name].shape}")
                    
                except Exception as stack_error:
                    print(f"[错误] ❌ 字段 {field_name} 堆叠失败: {stack_error}")
                    
                    # 尝试使用cat
                    try:
                        result[field_name] = torch.cat(field_data, dim=0)
                        print(f"[修复] 🔧 字段 {field_name} 使用cat成功")
                    except Exception as cat_error:
                        print(f"[错误] ❌ 字段 {field_name} cat也失败: {cat_error}")
                        # 使用第一个元素作为默认值
                        result[field_name] = field_data[0].unsqueeze(0)
                        print(f"[默认] 使用第一个元素作为默认值")
            
        except Exception as e:
            print(f"[错误] ❌ 字段 {field_name} 处理失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 创建默认张量
            result[field_name] = torch.tensor([0], dtype=torch.float32)
    
    print(f"[调试] ✅ collate完成，返回字段: {list(result.keys())}")
    return result


# 导出函数供外部使用
__all__ = ['debug_collate_fn', 'safe_tensor_creation']
