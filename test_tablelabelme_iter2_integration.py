#!/usr/bin/env python3
"""
TableLabelMe格式支持集成测试 - 迭代2
验证目录扫描和文件索引构建功能

该测试脚本验证迭代2的所有核心功能：
1. FileScanner模块的独立功能
2. TableLabelMe数据集的集成功能
3. 与迭代1的兼容性
4. 端到端流程的完整性
"""

import sys
import os
import time
import tracemalloc
import hashlib
from typing import Dict, Any, List, Optional

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 导入测试所需的模块
try:
    from lib.datasets.parsers import FileScanner, TableLabelMeParser
    from lib.datasets.dataset.table_labelmev2 import Table
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此测试脚本")
    sys.exit(1)


class TestConfig:
    """测试配置类"""
    # 真实数据路径
    REAL_DATA_PATH = "D:/workspace/datasets/cf_train_clean/wired_tables_reorganized/TabRecSet_TableLabelMe_fix/chinese"
    
    # 性能基准
    SCAN_TIME_LIMIT = 10.0  # 1000文件<10秒的要求
    MEMORY_LIMIT_MB = 500   # 内存限制（MB）
    
    # 测试数据预期值
    EXPECTED_PART_DIRS = 5  # 预期的part目录数量
    EXPECTED_SAMPLES = 2272  # 预期的样本数量


class MockOpt:
    """模拟配置对象"""
    def __init__(self):
        self.data_dir = 'real_data'
        self.dataset_name = 'TableLabelMe'


class TestReporter:
    """测试报告器"""
    def __init__(self):
        self.test_results = []
        self.start_time = time.time()
    
    def add_result(self, test_name: str, passed: bool, message: str = "", duration: float = 0.0):
        """添加测试结果"""
        self.test_results.append({
            'test_name': test_name,
            'passed': passed,
            'message': message,
            'duration': duration
        })
    
    def print_summary(self):
        """打印测试摘要"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        failed_tests = total_tests - passed_tests
        total_duration = time.time() - self.start_time
        
        print("\n" + "="*60)
        print("迭代2集成测试报告")
        print("="*60)
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"总耗时: {total_duration:.2f}秒")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result['passed']:
                    print(f"  ❌ {result['test_name']}: {result['message']}")
        
        print("\n详细结果:")
        for result in self.test_results:
            status = "✅" if result['passed'] else "❌"
            print(f"  {status} {result['test_name']} ({result['duration']:.2f}s)")
            if result['message']:
                print(f"      {result['message']}")


def test_file_scanner():
    """测试FileScanner独立功能"""
    print("=== 测试FileScanner模块 ===")
    reporter = TestReporter()
    
    try:
        # 测试1: FileScanner创建
        start_time = time.time()
        scanner = FileScanner()
        duration = time.time() - start_time
        reporter.add_result("FileScanner创建", True, "成功创建FileScanner实例", duration)
        
        # 测试2: 配置验证
        start_time = time.time()
        config_valid = (
            isinstance(scanner.config, dict) and
            'part_pattern' in scanner.config and
            'image_extensions' in scanner.config
        )
        duration = time.time() - start_time
        reporter.add_result("配置验证", config_valid, 
                          "配置结构正确" if config_valid else "配置结构错误", duration)
        
        # 测试3: 路径规范化
        start_time = time.time()
        test_path = "test/path/../normalized"
        normalized = scanner._normalize_path(test_path)
        path_valid = os.path.isabs(normalized)
        duration = time.time() - start_time
        reporter.add_result("路径规范化", path_valid, 
                          f"路径规范化正确: {normalized}" if path_valid else "路径规范化失败", duration)
        
        # 测试4: ID生成
        start_time = time.time()
        test_image_path = "test_image.jpg"
        image_id1 = scanner._generate_image_id(test_image_path)
        image_id2 = scanner._generate_image_id(test_image_path)
        id_valid = (isinstance(image_id1, int) and image_id1 == image_id2)
        duration = time.time() - start_time
        reporter.add_result("ID生成", id_valid, 
                          f"ID生成稳定: {image_id1}" if id_valid else "ID生成不稳定", duration)
        
        # 测试5: 真实数据扫描
        if os.path.exists(TestConfig.REAL_DATA_PATH):
            start_time = time.time()
            scan_result = scanner.scan_directories([TestConfig.REAL_DATA_PATH], 'train')
            duration = time.time() - start_time
            
            file_index = scan_result.get('file_index', {})
            statistics = scan_result.get('statistics', {})
            
            scan_valid = (
                len(file_index) > 0 and
                statistics.get('valid_pairs', 0) > 0 and
                statistics.get('part_directories', 0) == TestConfig.EXPECTED_PART_DIRS
            )
            
            message = f"扫描到{len(file_index)}个文件对，{statistics.get('part_directories', 0)}个part目录"
            reporter.add_result("真实数据扫描", scan_valid, message, duration)
        else:
            reporter.add_result("真实数据扫描", False, f"数据路径不存在: {TestConfig.REAL_DATA_PATH}", 0)
        
    except Exception as e:
        reporter.add_result("FileScanner测试", False, f"异常: {str(e)}", 0)
    
    reporter.print_summary()
    return reporter.test_results


def test_dataset_integration():
    """测试数据集集成功能"""
    print("\n=== 测试数据集集成 ===")
    reporter = TestReporter()
    
    try:
        opt = MockOpt()
        
        # 测试1: 数据集创建
        start_time = time.time()
        dataset = Table(opt, 'train')
        duration = time.time() - start_time
        
        creation_valid = (
            hasattr(dataset, 'file_scanner') and
            hasattr(dataset, 'file_index') and
            hasattr(dataset, 'scan_statistics')
        )
        reporter.add_result("数据集创建", creation_valid, 
                          "数据集创建成功" if creation_valid else "数据集创建失败", duration)
        
        # 测试2: 样本数量验证
        start_time = time.time()
        sample_count = len(dataset)
        count_valid = sample_count == TestConfig.EXPECTED_SAMPLES
        duration = time.time() - start_time
        reporter.add_result("样本数量", count_valid, 
                          f"样本数量: {sample_count}" if count_valid else f"样本数量不匹配: {sample_count}", duration)
        
        # 测试3: 文件索引格式验证
        start_time = time.time()
        if len(dataset.file_index) > 0:
            first_id = next(iter(dataset.file_index))
            first_entry = dataset.file_index[first_id]
            
            required_fields = ['image_path', 'annotation_path', 'part_dir', 'dataset_source', 'relative_path']
            format_valid = all(field in first_entry for field in required_fields)
        else:
            format_valid = False
        duration = time.time() - start_time
        reporter.add_result("文件索引格式", format_valid, 
                          "索引格式正确" if format_valid else "索引格式错误", duration)
        
        # 测试4: 扫描统计验证
        start_time = time.time()
        stats = dataset.scan_statistics
        stats_valid = (
            stats.get('valid_pairs', 0) > 0 and
            stats.get('success_rate', 0) == 100.0 and
            stats.get('orphan_images', 0) == 0
        )
        duration = time.time() - start_time
        message = f"有效对: {stats.get('valid_pairs', 0)}, 成功率: {stats.get('success_rate', 0)}%"
        reporter.add_result("扫描统计", stats_valid, message, duration)
        
    except Exception as e:
        reporter.add_result("数据集集成测试", False, f"异常: {str(e)}", 0)
    
    reporter.print_summary()
    return reporter.test_results


def test_compatibility():
    """测试与迭代1的兼容性"""
    print("\n=== 测试向后兼容性 ===")
    reporter = TestReporter()

    try:
        opt = MockOpt()

        # 测试1: 接口兼容性
        start_time = time.time()
        dataset = Table(opt, 'train')

        # 检查关键接口是否存在
        interface_valid = (
            hasattr(dataset, '__len__') and
            hasattr(dataset, '__getitem__') and
            hasattr(dataset, 'file_index') and
            hasattr(dataset, 'images') and
            hasattr(dataset, 'annotations')
        )
        duration = time.time() - start_time
        reporter.add_result("接口兼容性", interface_valid,
                          "接口完全兼容" if interface_valid else "接口不兼容", duration)

        # 测试2: 数据结构兼容性
        start_time = time.time()
        if len(dataset.file_index) > 0:
            first_id = next(iter(dataset.file_index))

            # 检查必需的字段
            entry = dataset.file_index[first_id]
            structure_valid = (
                'image_path' in entry and
                'annotation_path' in entry and
                isinstance(first_id, int)
            )
        else:
            structure_valid = False
        duration = time.time() - start_time
        reporter.add_result("数据结构兼容性", structure_valid,
                          "数据结构兼容" if structure_valid else "数据结构不兼容", duration)

        # 测试3: 数据加载兼容性
        start_time = time.time()
        if len(dataset) > 0:
            # 检查图像信息和标注数据的结构
            image_info_valid = (
                len(dataset.image_info) == len(dataset) and
                len(dataset.annotations) == len(dataset) and
                len(dataset.images) == len(dataset)
            )
        else:
            image_info_valid = False
        duration = time.time() - start_time
        reporter.add_result("数据加载兼容性", image_info_valid,
                          "数据加载兼容" if image_info_valid else "数据加载不兼容", duration)

    except Exception as e:
        reporter.add_result("兼容性测试", False, f"异常: {str(e)}", 0)

    reporter.print_summary()
    return reporter.test_results


def test_end_to_end():
    """测试端到端流程"""
    print("\n=== 测试端到端流程 ===")
    reporter = TestReporter()

    try:
        # 测试1: 完整数据加载流程
        start_time = time.time()
        opt = MockOpt()

        # 创建train和val数据集
        train_dataset = Table(opt, 'train')
        val_dataset = Table(opt, 'val')

        flow_valid = (
            len(train_dataset) > 0 and
            len(val_dataset) > 0 and
            hasattr(train_dataset, 'scan_statistics') and
            hasattr(val_dataset, 'scan_statistics')
        )
        duration = time.time() - start_time
        message = f"Train: {len(train_dataset)}, Val: {len(val_dataset)}"
        reporter.add_result("完整数据加载", flow_valid, message, duration)

        # 测试2: 性能基准测试
        start_time = time.time()
        scanner = FileScanner()
        scan_result = scanner.scan_directories([TestConfig.REAL_DATA_PATH], 'test')
        scan_duration = time.time() - start_time

        # 计算每1000文件的扫描时间
        file_count = len(scan_result.get('file_index', {}))
        if file_count > 0:
            time_per_1000 = (scan_duration / file_count) * 1000
            performance_valid = time_per_1000 < TestConfig.SCAN_TIME_LIMIT
        else:
            performance_valid = False

        message = f"扫描{file_count}文件耗时{scan_duration:.2f}s，每1000文件{time_per_1000:.2f}s"
        reporter.add_result("性能基准", performance_valid, message, scan_duration)

        # 测试3: 内存使用测试
        tracemalloc.start()
        start_time = time.time()

        # 执行内存密集操作
        test_dataset = Table(opt, 'train')
        _ = len(test_dataset)

        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()
        duration = time.time() - start_time

        peak_mb = peak / 1024 / 1024
        memory_valid = peak_mb < TestConfig.MEMORY_LIMIT_MB

        message = f"峰值内存使用: {peak_mb:.1f}MB"
        reporter.add_result("内存使用", memory_valid, message, duration)

        # 测试4: 错误处理测试
        start_time = time.time()
        try:
            # 测试不存在的路径
            error_scanner = FileScanner()
            error_result = error_scanner.scan_directories(['/nonexistent/path'], 'test')

            # 应该返回空结果而不是崩溃
            error_handling_valid = (
                isinstance(error_result, dict) and
                len(error_result.get('file_index', {})) == 0
            )
        except Exception:
            error_handling_valid = False

        duration = time.time() - start_time
        reporter.add_result("错误处理", error_handling_valid,
                          "错误处理正确" if error_handling_valid else "错误处理失败", duration)

    except Exception as e:
        reporter.add_result("端到端测试", False, f"异常: {str(e)}", 0)

    reporter.print_summary()
    return reporter.test_results


def main():
    """运行所有测试"""
    print("TableLabelMe格式支持集成测试 - 迭代2")
    print("="*60)

    # 检查数据路径
    if not os.path.exists(TestConfig.REAL_DATA_PATH):
        print(f"警告: 真实数据路径不存在: {TestConfig.REAL_DATA_PATH}")
        print("某些测试可能会失败")

    # 运行所有测试
    all_results = []

    print("\n开始执行集成测试...")
    all_results.extend(test_file_scanner())
    all_results.extend(test_dataset_integration())
    all_results.extend(test_compatibility())
    all_results.extend(test_end_to_end())

    # 生成最终报告
    print("\n" + "="*60)
    print("最终测试报告")
    print("="*60)

    total_tests = len(all_results)
    passed_tests = sum(1 for result in all_results if result['passed'])
    failed_tests = total_tests - passed_tests

    print(f"总测试数: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {failed_tests}")
    print(f"成功率: {(passed_tests/total_tests*100):.1f}%")

    if failed_tests == 0:
        print("\n🎉 所有测试通过！迭代2集成测试成功！")
        return 0
    else:
        print(f"\n❌ {failed_tests}个测试失败，请检查问题")
        return 1


if __name__ == "__main__":
    sys.exit(main())
