#!/usr/bin/env python3
"""
验证脚本 - 步骤3.1：日志配置模块测试
测试LoggerConfig模块的基本功能
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_logger_config():
    """测试日志配置功能"""
    try:
        from lib.utils.logger_config import LoggerConfig
        
        print("=== 测试日志配置模块 ===")
        print()
        
        # 测试1：默认配置
        print("1. 测试默认配置...")
        default_config = LoggerConfig.get_default_config()
        print(f"   默认配置: {default_config}")
        
        # 测试2：创建默认日志记录器
        print("2. 测试默认日志记录器...")
        logger = LoggerConfig.setup_logger('test_logger')
        logger.info('测试信息日志')
        logger.warning('测试警告日志')
        logger.error('测试错误日志')
        print("   ✅ 默认日志记录器创建成功")
        
        # 测试3：自定义配置
        print("3. 测试自定义配置...")
        custom_config = {
            "level": "DEBUG",
            "format": "[%(asctime)s] %(levelname)s [%(name)s] %(message)s",
            "date_format": "%Y-%m-%d %H:%M:%S",
            "console_output": True,
            "file_output": False
        }
        custom_logger = LoggerConfig.setup_logger('custom_logger', custom_config)
        custom_logger.debug('测试调试日志')
        custom_logger.info('测试自定义配置信息日志')
        print("   ✅ 自定义日志记录器创建成功")
        
        # 测试4：多个日志记录器
        print("4. 测试多个日志记录器...")
        logger1 = LoggerConfig.setup_logger('logger1')
        logger2 = LoggerConfig.setup_logger('logger2')
        logger1.info('来自logger1的消息')
        logger2.info('来自logger2的消息')
        print("   ✅ 多个日志记录器创建成功")
        
        print()
        print("🎉 所有测试通过！日志配置模块验证成功！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = test_logger_config()
    exit(0 if success else 1)
