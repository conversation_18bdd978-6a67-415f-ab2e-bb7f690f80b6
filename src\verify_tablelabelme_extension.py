#!/usr/bin/env python3
"""
步骤五验证脚本：验证TableLabelMe特有字段提取功能
"""

def verify_tablelabelme_extension():
    """验证TableLabelMeCTDetDataset扩展功能的正确性"""
    
    print("=== 步骤五：TableLabelMe特有字段提取验证 ===\n")
    
    try:
        from lib.opts import opts
        from lib.datasets.sample.table_ctdet import TableLabelMeCTDetDataset
        
        # 测试1：默认配置下的扩展功能状态
        print("1. 测试默认配置下的扩展功能状态:")
        opt = opts().parse(['ctdet_mid'])
        dataset = TableLabelMeCTDetDataset(opt, 'train')
        
        print(f"   Header prediction: {dataset.enable_header_prediction}")
        print(f"   Content prediction: {dataset.enable_content_prediction}")
        print(f"   Border prediction: {dataset.enable_border_prediction}")
        
        # 验证默认状态
        if (not dataset.enable_header_prediction and 
            not dataset.enable_content_prediction and 
            not dataset.enable_border_prediction):
            print("   ✅ 默认扩展功能状态验证通过")
        else:
            print("   ❌ 默认扩展功能状态验证失败")
            return False
        
        # 测试2：启用单个扩展功能
        print("\n2. 测试启用单个扩展功能:")
        opt = opts().parse(['ctdet_mid', '--enable_header_prediction'])
        dataset = TableLabelMeCTDetDataset(opt, 'train')
        
        print(f"   Header prediction: {dataset.enable_header_prediction}")
        print(f"   Content prediction: {dataset.enable_content_prediction}")
        print(f"   Border prediction: {dataset.enable_border_prediction}")
        
        if (dataset.enable_header_prediction and 
            not dataset.enable_content_prediction and 
            not dataset.enable_border_prediction):
            print("   ✅ 单个扩展功能启用验证通过")
        else:
            print("   ❌ 单个扩展功能启用验证失败")
            return False
        
        # 测试3：启用多个扩展功能
        print("\n3. 测试启用多个扩展功能:")
        opt = opts().parse(['ctdet_mid', '--enable_header_prediction', '--enable_content_prediction'])
        dataset = TableLabelMeCTDetDataset(opt, 'train')
        
        print(f"   Header prediction: {dataset.enable_header_prediction}")
        print(f"   Content prediction: {dataset.enable_content_prediction}")
        print(f"   Border prediction: {dataset.enable_border_prediction}")
        
        if (dataset.enable_header_prediction and 
            dataset.enable_content_prediction and 
            not dataset.enable_border_prediction):
            print("   ✅ 多个扩展功能启用验证通过")
        else:
            print("   ❌ 多个扩展功能启用验证失败")
            return False
        
        # 测试4：启用所有扩展功能
        print("\n4. 测试启用所有扩展功能:")
        opt = opts().parse(['ctdet_mid', '--enable_header_prediction', '--enable_content_prediction', '--enable_border_prediction'])
        dataset = TableLabelMeCTDetDataset(opt, 'train')
        
        print(f"   Header prediction: {dataset.enable_header_prediction}")
        print(f"   Content prediction: {dataset.enable_content_prediction}")
        print(f"   Border prediction: {dataset.enable_border_prediction}")
        
        if (dataset.enable_header_prediction and 
            dataset.enable_content_prediction and 
            dataset.enable_border_prediction):
            print("   ✅ 所有扩展功能启用验证通过")
        else:
            print("   ❌ 所有扩展功能启用验证失败")
            return False
        
        # 测试5：验证扩展信息方法
        print("\n5. 测试扩展信息方法:")
        ext_info = dataset.get_extension_info()
        print(f"   扩展信息: {ext_info}")
        
        expected_keys = ['class_name', 'parent_class', 'enable_header_prediction', 
                        'enable_content_prediction', 'enable_border_prediction', 'extension_active']
        
        if all(key in ext_info for key in expected_keys):
            print("   ✅ 扩展信息方法验证通过")
        else:
            print("   ❌ 扩展信息方法验证失败")
            return False
        
        # 测试6：验证扩展功能开关机制
        print("\n6. 测试扩展功能开关机制:")
        
        # 测试无扩展功能时的行为
        opt_no_ext = opts().parse(['ctdet_mid'])
        dataset_no_ext = TableLabelMeCTDetDataset(opt_no_ext, 'train')
        
        # 模拟__getitem__调用中的扩展功能检查
        any_extension_enabled = any([
            dataset_no_ext.enable_header_prediction,
            dataset_no_ext.enable_content_prediction,
            dataset_no_ext.enable_border_prediction
        ])
        print(f"   无扩展功能时，any()检查结果: {any_extension_enabled}")
        
        # 测试有扩展功能时的行为
        opt_with_ext = opts().parse(['ctdet_mid', '--enable_header_prediction'])
        dataset_with_ext = TableLabelMeCTDetDataset(opt_with_ext, 'train')
        
        any_extension_enabled_with = any([
            dataset_with_ext.enable_header_prediction,
            dataset_with_ext.enable_content_prediction,
            dataset_with_ext.enable_border_prediction
        ])
        print(f"   有扩展功能时，any()检查结果: {any_extension_enabled_with}")
        
        if not any_extension_enabled and any_extension_enabled_with:
            print("   ✅ 扩展功能开关机制验证通过")
        else:
            print("   ❌ 扩展功能开关机制验证失败")
            return False
        
        # 测试7：验证类的字符串表示
        print("\n7. 测试类的字符串表示:")
        dataset_repr = str(dataset)
        print(f"   数据集字符串表示: {dataset_repr}")
        
        if 'TableLabelMeCTDetDataset' in dataset_repr and 'extensions_active=True' in dataset_repr:
            print("   ✅ 类字符串表示验证通过")
        else:
            print("   ❌ 类字符串表示验证失败")
            return False
        
        print("\n=== 验证总结 ===")
        print("✅ TableLabelMeCTDetDataset扩展功能验证通过")
        print("✅ 参数正确传递到数据集实例")
        print("✅ 扩展功能开关机制工作正常")
        print("✅ 扩展信息方法工作正常")
        print("✅ 类字符串表示包含扩展状态")
        print("✅ 为未来的多任务预测提供了架构基础")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_tablelabelme_extension()
    if success:
        print("\n🎉 TableLabelMe特有字段提取验证完全通过！")
    else:
        print("\n❌ TableLabelMe特有字段提取验证失败，需要进一步检查。")
