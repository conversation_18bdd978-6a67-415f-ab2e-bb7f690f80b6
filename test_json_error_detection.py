#!/usr/bin/env python3
"""
测试JSON错误检测功能
"""

import sys
import os
import json
import tempfile

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from lib.utils.logger_config import LoggerConfig
from lib.datasets.parsers.quality_filter import QualityFilter

def test_json_error_detection():
    """测试JSON错误检测"""
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建JSON格式错误的文件
        image_path = os.path.join(temp_dir, 'test.jpg')
        annotation_path = os.path.join(temp_dir, 'test.json')
        
        with open(image_path, 'w') as f:
            f.write('fake image')
        
        # 创建一个明确的JSON语法错误
        bad_json_content = '{"quality": "合格", "bbox": {'
        with open(annotation_path, 'w', encoding='utf-8') as f:
            f.write(bad_json_content)
        
        print('测试JSON文件内容:')
        with open(annotation_path, 'r', encoding='utf-8') as f:
            print(repr(f.read()))
        
        # 直接测试JSON解析
        try:
            with open(annotation_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print('JSON解析成功:', data)
        except json.JSONDecodeError as e:
            print('JSON解析失败:', str(e))
            print('行号:', getattr(e, 'lineno', -1))
        
        # 测试质量筛选器
        logger = LoggerConfig.setup_logger('test_json_error')
        quality_filter = QualityFilter(logger=logger)
        
        test_index = {1: {'image_path': image_path, 'annotation_path': annotation_path}}
        result = quality_filter.filter_samples(test_index, 'test')
        
        print('筛选结果:')
        print('JSON语法错误数量:', len(result['exception_report']['format_errors']['json_syntax_errors']))
        print('文件访问错误数量:', len(result['exception_report']['file_access_errors']))
        
        if len(result['exception_report']['format_errors']['json_syntax_errors']) > 0:
            print('✅ JSON错误检测正常')
            return True
        else:
            print('❌ JSON错误检测失败')
            return False

if __name__ == "__main__":
    test_json_error_detection()
