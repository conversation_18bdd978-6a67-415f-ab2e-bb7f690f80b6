#!/usr/bin/env python3
"""
步骤三验证脚本：验证Table_labelmev2类中自定义方法的移除
"""

def verify_method_removal():
    """验证Table_labelmev2类中方法移除的正确性"""
    
    print("=== 步骤三：Table_labelmev2方法移除验证 ===\n")
    
    # 验证1：检查文件内容
    print("1. 检查table_labelmev2.py文件内容:")
    
    try:
        with open('lib/datasets/dataset/table_labelmev2.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查已移除的方法
        removed_methods = [
            '__getitem__',
            '_get_transform_params',
            '_get_transform_matrices', 
            '_preprocess_image',
            '_create_empty_sample',
            '_load_and_parse_annotation',
            '_apply_data_augmentation',
            '_generate_training_targets',
            '_draw_gaussian'
        ]
        
        print("   已移除的方法检查:")
        for method in removed_methods:
            method_exists = f'def {method}(' in content
            print(f"   {'❌' if not method_exists else '⚠️'} {method}: {'已移除' if not method_exists else '仍存在'}")
        
        # 检查保留的COCO API方法
        preserved_methods = [
            'getImgIds',
            'loadImgs', 
            'getAnnIds',
            'loadAnns',
            'convert_eval_format',
            'save_results',
            'run_eval'
        ]
        
        print("\n   保留的COCO API方法检查:")
        for method in preserved_methods:
            method_exists = f'def {method}(' in content
            print(f"   {'✅' if method_exists else '❌'} {method}: {'已保留' if method_exists else '已丢失'}")
        
        # 检查说明注释
        has_explanation = "本类不再提供__getitem__方法" in content
        print(f"\n   ✅ 说明注释添加: {has_explanation}")
        
    except Exception as e:
        print(f"   ❌ 文件检查失败: {e}")
        return False
    
    # 验证2：类导入和基础功能
    print("\n2. 验证类导入和基础功能:")
    
    try:
        # 导入类
        import sys
        sys.path.append('.')
        from lib.datasets.dataset.table_labelmev2 import Table as Table_labelmev2
        print("   ✅ Table_labelmev2类导入成功")
        
        # 检查类的方法
        class_methods = [method for method in dir(Table_labelmev2) if not method.startswith('_') or method == '__getitem__']
        
        # 检查__getitem__方法是否已移除
        has_getitem = hasattr(Table_labelmev2, '__getitem__')
        print(f"   {'❌' if not has_getitem else '⚠️'} __getitem__方法: {'已移除' if not has_getitem else '仍存在'}")
        
        # 检查COCO API方法是否保留
        coco_methods = ['getImgIds', 'loadImgs', 'getAnnIds', 'loadAnns']
        for method in coco_methods:
            has_method = hasattr(Table_labelmev2, method)
            print(f"   {'✅' if has_method else '❌'} {method}方法: {'已保留' if has_method else '已丢失'}")
        
    except Exception as e:
        print(f"   ❌ 类验证失败: {e}")
        return False
    
    # 验证3：多重继承兼容性
    print("\n3. 验证多重继承兼容性:")
    
    try:
        # 模拟多重继承
        from lib.datasets.sample.table_ctdet import TableLabelMeCTDetDataset
        
        class TestTableLabelMeDataset(Table_labelmev2, TableLabelMeCTDetDataset):
            pass
        
        print("   ✅ 多重继承类创建成功")
        print(f"   继承链: {[cls.__name__ for cls in TestTableLabelMeDataset.__mro__]}")
        
        # 检查__getitem__方法来源
        has_getitem = hasattr(TestTableLabelMeDataset, '__getitem__')
        if has_getitem:
            getitem_source = None
            for cls in TestTableLabelMeDataset.__mro__:
                if '__getitem__' in cls.__dict__:
                    getitem_source = cls.__name__
                    break
            print(f"   ✅ __getitem__方法来源: {getitem_source}")
        else:
            print("   ❌ __getitem__方法不存在")
        
        # 检查COCO API方法来源
        coco_method_sources = {}
        for method in ['getImgIds', 'loadImgs']:
            for cls in TestTableLabelMeDataset.__mro__:
                if method in cls.__dict__:
                    coco_method_sources[method] = cls.__name__
                    break
        
        print(f"   ✅ COCO API方法来源: {coco_method_sources}")
        
    except Exception as e:
        print(f"   ❌ 多重继承验证失败: {e}")
        return False
    
    print("\n=== 验证总结 ===")
    print("✅ 自定义__getitem__方法及相关辅助方法已移除")
    print("✅ COCO API兼容接口完全保留")
    print("✅ 多重继承机制工作正常")
    print("✅ 数据处理逻辑将由TableLabelMeCTDetDataset提供")
    
    return True

if __name__ == "__main__":
    verify_method_removal()
