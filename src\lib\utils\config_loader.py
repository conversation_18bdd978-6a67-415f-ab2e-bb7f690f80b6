# src/lib/utils/config_loader.py
"""
配置文件加载模块 - TableLabelMe数据格式支持项目
提供配置文件的加载、验证和处理功能，为配置系统提供核心功能
"""

import os
import sys
import importlib.util
import logging
from typing import Dict, List, Tuple, Optional, Any, Union
from pathlib import Path

from .logger_config import LoggerConfig


class ConfigLoader:
    """
    配置文件加载器，提供配置文件的加载、验证和处理功能

    主要功能：
    1. 加载指定的配置文件并返回验证后的配置数据
    2. 验证配置数据的结构完整性
    3. 验证配置中所有路径的有效性和可访问性
    4. 路径规范化处理，转换为绝对路径
    5. 生成统一的配置对象，整合命令行参数和配置文件数据
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化配置加载器

        Args:
            logger (Optional[logging.Logger]): 日志记录器，如果为None则创建默认日志记录器
        """
        if logger is None:
            self.logger = LoggerConfig.setup_logger('ConfigLoader')
        else:
            self.logger = logger

        self.loaded_config = None
        self.validation_errors = []
        self.is_valid = False

        self.logger.info("ConfigLoader初始化完成")

    def load_config(self, config_path: str, config_name: Optional[str] = None) -> Dict[str, Any]:
        """
        加载指定的配置文件并返回验证后的配置数据

        Args:
            config_path (str): 配置文件的绝对路径
            config_name (Optional[str]): 要加载的配置名称（如"tableme_full"），如果为None则返回所有配置

        Returns:
            Dict[str, Any]: 验证后的配置数据字典

        Raises:
            FileNotFoundError: 配置文件不存在
            ImportError: 配置文件导入失败
            ValueError: 配置数据格式错误
        """
        self.logger.info(f"开始加载配置文件: {config_path}")

        # 检查配置文件存在性
        if not os.path.exists(config_path):
            error_msg = f"配置文件不存在: {config_path}"
            self.logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        # 导入配置模块
        try:
            config_module = self._import_config_module(config_path)
            self.logger.info("配置文件导入成功")
        except Exception as e:
            error_msg = f"配置文件导入失败: {e}"
            self.logger.error(error_msg)
            raise ImportError(error_msg)

        # 提取配置数据
        if not hasattr(config_module, 'DATASET_PATH_CONFIGS'):
            error_msg = "配置文件中缺少DATASET_PATH_CONFIGS字典"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        all_configs = config_module.DATASET_PATH_CONFIGS

        # 验证配置结构
        if not self.validate_config_structure(all_configs):
            error_msg = f"配置结构验证失败: {self.validation_errors}"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 如果指定了配置名称，提取特定配置
        if config_name:
            if config_name not in all_configs:
                error_msg = f"配置名称'{config_name}'不存在，可用配置: {list(all_configs.keys())}"
                self.logger.error(error_msg)
                raise ValueError(error_msg)

            config_data = all_configs[config_name]
            self.logger.info(f"成功提取配置'{config_name}'")
        else:
            config_data = all_configs
            self.logger.info("成功加载所有配置")

        # 验证路径
        if config_name:
            # 单个配置的路径验证
            all_paths = config_data.get('train', []) + config_data.get('val', [])
            is_valid, invalid_paths = self.validate_paths(all_paths)
            if not is_valid:
                self.logger.warning(f"配置'{config_name}'中存在无效路径: {invalid_paths}")

        self.loaded_config = config_data
        self.is_valid = True
        self.logger.info("配置加载完成")

        return config_data

    def validate_config_structure(self, config_data: Dict[str, Any]) -> bool:
        """
        验证配置数据的结构完整性

        Args:
            config_data (Dict[str, Any]): 需要验证的配置数据

        Returns:
            bool: 验证结果，True表示结构正确
        """
        self.validation_errors.clear()

        if not isinstance(config_data, dict):
            self.validation_errors.append("配置数据必须是字典类型")
            return False

        # 验证每个配置项
        for config_name, config_item in config_data.items():
            if not isinstance(config_item, dict):
                self.validation_errors.append(f"配置项'{config_name}'必须是字典类型")
                continue

            # 检查必需的键
            required_keys = ['train', 'val']
            for key in required_keys:
                if key not in config_item:
                    self.validation_errors.append(f"配置项'{config_name}'缺少必需字段'{key}'")
                elif not isinstance(config_item[key], list):
                    self.validation_errors.append(f"配置项'{config_name}'的字段'{key}'必须是列表类型")

        is_valid = len(self.validation_errors) == 0
        if is_valid:
            self.logger.info("配置结构验证通过")
        else:
            self.logger.error(f"配置结构验证失败: {self.validation_errors}")

        return is_valid

    def validate_paths(self, path_list: List[str]) -> Tuple[bool, List[str]]:
        """
        验证配置中所有路径的有效性和可访问性

        Args:
            path_list (List[str]): 需要验证的路径列表

        Returns:
            Tuple[bool, List[str]]: (验证结果, 无效路径列表)
        """
        invalid_paths = []

        for path in path_list:
            if not isinstance(path, str):
                invalid_paths.append(f"路径不是字符串类型: {path}")
                continue

            # 检查是否为绝对路径
            if not os.path.isabs(path):
                invalid_paths.append(f"路径不是绝对路径: {path}")
                continue

            # 检查路径存在性
            if not os.path.exists(path):
                invalid_paths.append(f"路径不存在: {path}")
                continue

            # 检查路径权限
            if not os.access(path, os.R_OK):
                invalid_paths.append(f"路径不可读: {path}")
                continue

        is_valid = len(invalid_paths) == 0

        if is_valid:
            self.logger.info(f"路径验证通过，共验证{len(path_list)}个路径")
        else:
            self.logger.warning(f"路径验证发现{len(invalid_paths)}个问题: {invalid_paths}")

        return is_valid, invalid_paths

    def normalize_paths(self, path_list: List[str]) -> List[str]:
        """
        路径规范化处理，转换为绝对路径

        Args:
            path_list (List[str]): 需要规范化的路径列表

        Returns:
            List[str]: 规范化后的路径列表
        """
        normalized_paths = []

        for path in path_list:
            if not isinstance(path, str):
                self.logger.warning(f"跳过非字符串路径: {path}")
                continue

            # 转换为绝对路径
            abs_path = os.path.abspath(path)

            # 规范化路径分隔符
            normalized_path = os.path.normpath(abs_path)

            normalized_paths.append(normalized_path)

        self.logger.info(f"路径规范化完成，处理{len(path_list)}个路径")
        return normalized_paths

    def generate_config_object(self, args: Any, config_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成统一的配置对象，整合命令行参数和配置文件数据

        Args:
            args: 命令行参数对象
            config_data (Dict[str, Any]): 验证后的配置数据

        Returns:
            Dict[str, Any]: 统一配置对象
        """
        self.logger.info("开始生成统一配置对象")

        # 规范化路径
        train_paths = self.normalize_paths(config_data.get('train', []))
        val_paths = self.normalize_paths(config_data.get('val', []))

        # 生成统一配置对象
        unified_config = {
            "dataset_mode": "TableLabelMe",
            "data_paths": {
                "train": train_paths,
                "val": val_paths
            },
            "dataset_config_name": getattr(args, 'config_name', 'unknown'),
            "original_args": args,
            "config_metadata": {
                "config_file_path": getattr(args, 'data_config', None),
                "load_timestamp": __import__('time').time(),
                "validation_status": "valid" if self.is_valid else "invalid"
            }
        }

        self.logger.info("统一配置对象生成完成")
        return unified_config

    def _import_config_module(self, config_path: str):
        """
        导入配置模块的私有方法

        Args:
            config_path (str): 配置文件路径

        Returns:
            module: 导入的配置模块
        """
        spec = importlib.util.spec_from_file_location("config_module", config_path)
        if spec is None or spec.loader is None:
            raise ImportError(f"无法创建模块规范: {config_path}")

        config_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config_module)

        return config_module

    def _check_path_existence(self, path: str) -> bool:
        """
        检查路径存在性的私有方法

        Args:
            path (str): 要检查的路径

        Returns:
            bool: 路径是否存在
        """
        return os.path.exists(path)

    def _convert_to_absolute_path(self, path: str) -> str:
        """
        转换为绝对路径的私有方法

        Args:
            path (str): 相对路径或绝对路径

        Returns:
            str: 绝对路径
        """
        return os.path.abspath(path)

    def _log_validation_results(self, results: Dict[str, Any]) -> None:
        """
        记录验证结果的私有方法

        Args:
            results (Dict[str, Any]): 验证结果字典
        """
        if results.get('success', False):
            self.logger.info(f"验证成功: {results.get('message', '')}")
        else:
            self.logger.error(f"验证失败: {results.get('message', '')}")