# LORE-TSR项目TableLabelMe数据格式支持需求规划

## 1. 项目概述

### 1.1 规划目标
为LORE-TSR项目增量支持TableLabelMe数据格式，在保持与原有COCO格式完全兼容的前提下，实现多源异构数据集的统一训练能力。

### 1.2 核心约束
- **算法一致性**：两种数据格式训练结果必须完全一致
- **无侵害性**：对原项目核心逻辑（数据预处理、模型定义、损失函数）零修改
- **向后兼容**：完全保持对原有COCO格式的支持
- **接口一致性**：Dataset.__getitem__返回的数据结构保持兼容

### 1.3 技术风险评估
- **最高风险**：算法一致性保证，需要严格验证格式转换的正确性
- **中等风险**：多源数据配置的复杂性，需要robust的错误处理机制
- **低风险**：配置系统扩展，基于现有参数体系进行增量修改

## 2. 迭代开发计划

### 迭代1：基础格式解析器（MVP）

**目标**：实现TableLabelMe格式的基础解析和转换功能

**核心功能**：
- 创建TableLabelMe格式解析器类 `TableLabelMeParser`
- 实现bbox.p1-p4到segmentation的坐标转换
- 实现lloc字段到logic_axis的结构转换
- 处理cell_ind和文件名关联生成全局唯一ID

**具体任务**：
1. 解析JSON文件结构，提取关键字段（bbox、lloc、cell_ind、quality等）
2. 实现坐标转换算法：将四个角点(p1-p4)转换为一维segmentation数组
3. 实现逻辑结构转换：将start_row/end_row/start_col/end_col组合为logic_axis
4. 实现ID生成策略：基于image_id + cell_ind生成全局唯一annotation_id
5. 保留额外信息字段（table_ind、type、border、content等）

**输入接口**：
- 单个TableLabelMe JSON文件路径
- 对应的图像文件路径

**输出接口**：
- 标准化的数据字典，结构与COCO格式Dataset.__getitem__返回一致

**验收标准**：
- 单文件解析成功率100%
- 转换后数据结构字段完整性检查通过
- 坐标转换数学正确性验证通过

**技术约束**：
- 不依赖任何现有LORE-TSR模块，保持独立性
- 异常处理：JSON格式错误时返回None而非抛出异常

---

### 迭代2：数据集扫描和索引构建

**目标**：实现完整TableLabelMe数据集的目录扫描和文件索引构建

**核心功能**：
- 递归扫描part_xxxx目录结构
- 构建图像-标注文件映射关系
- 生成全局唯一的image_id标识

**具体任务**：
1. 实现目录遍历算法，识别符合part_xxxx模式的子目录
2. 扫描每个part目录下的图像文件（.jpg/.png）和标注文件（.json/_table_annotation.json）
3. 建立文件名匹配逻辑，处理两种标注文件命名模式
4. 实现基于文件路径的哈希算法，生成稳定的image_id
5. 构建完整的文件索引数据结构

**输入接口**：
- 数据根目录路径列表（支持多源数据）

**输出接口**：
- 文件索引字典：{image_id: {"image_path": str, "annotation_path": str}}
- 统计信息：总文件数、有效配对数、异常文件数

**验收标准**：
- 能够正确识别所有有效的图像-标注文件对
- image_id生成的唯一性和稳定性验证
- 处理10万+文件规模的性能测试通过

**技术约束**：
- 内存使用：索引构建过程中内存占用合理
- 性能要求：大数据集扫描时间在可接受范围内

---

### 迭代3：质量筛选和错误处理

**目标**：实现数据质量控制机制和完善的异常处理

**核心功能**：
- 基于quality字段的数据筛选
- 文件缺失和格式异常的处理
- 详细的日志记录机制

**具体任务**：
1. 实现quality字段检查，只保留"合格"标记的样本
2. 处理文件缺失情况：图像无标注、标注无图像、文件损坏
3. 处理JSON格式异常：语法错误、字段缺失、数据类型错误
4. 实现分级日志系统：INFO（统计信息）、WARNING（可恢复异常）、ERROR（严重错误）
5. 添加异常统计和报告功能

**输入接口**：
- 原始文件索引（来自迭代2）
- 质量筛选配置参数

**输出接口**：
- 过滤后的有效数据索引
- 异常统计报告：各类异常的数量和详细信息
- 结构化日志输出

**验收标准**：
- 异常情况下程序稳定运行，不崩溃
- 日志记录完整准确，便于问题排查
- 质量筛选逻辑正确，统计数据准确

**技术约束**：
- 错误恢复：单个文件异常不影响整体处理流程
- 日志格式：与现有LORE-TSR日志系统保持一致

---

### 迭代4：配置系统集成

**目标**：将TableLabelMe支持集成到现有的LORE-TSR配置系统

**核心功能**：
- 扩展dataset参数识别逻辑
- 新增data_config参数支持
- 实现参数兼容性处理

**具体任务**：
1. 修改现有参数解析逻辑，支持--dataset table + --dataset_name TableLabelMe组合
2. 新增--data_config参数，接受配置文件绝对路径
3. 创建configs/dataset_configs.py配置文件模板和解析逻辑
4. 实现参数兼容性处理：TableLabelMe模式下忽略--image_dir和--anno_path
5. 集成多源数据路径配置功能

**输入接口**：
- 命令行参数（扩展后的参数集）
- 外部配置文件（dataset_configs.py）

**输出接口**：
- 统一的配置对象，包含数据路径和格式信息
- 参数验证结果和错误提示

**验收标准**：
- 命令行参数解析正确，支持新旧两种模式
- 配置文件加载和验证功能正常
- 参数冲突检测和提示机制有效

**技术约束**：
- 向后兼容：原有COCO格式的所有参数组合仍然有效
- 配置隔离：TableLabelMe配置不影响COCO格式的参数处理

---

### 迭代5：训练流程集成和兼容性验证

**目标**：确保TableLabelMe格式在完整训练流程中正常工作

**核心功能**：
- 创建TableLabelMe Dataset类
- 集成到现有数据加载工厂
- 确保训练流程无缝运行

**具体任务**：
1. 创建TableLabelMeDataset类，继承现有Dataset基类
2. 实现__getitem__方法，返回与COCO格式完全兼容的数据结构
3. 集成前面迭代的所有功能：解析器、索引构建、质量筛选
4. 修改get_dataset工厂函数，支持TableLabelMe格式创建
5. 确保数据预处理、坐标变换等下游流程无需修改

**输入接口**：
- 配置参数（来自迭代4）
- 数据路径信息

**输出接口**：
- 可用于训练的Dataset实例
- 与COCO格式完全兼容的数据接口

**验收标准**：
- 完整训练流程运行成功，无报错
- 模型收敛正常，训练指标合理
- 算法一致性验证：相同数据的两种格式训练结果一致

**技术约束**：
- 接口兼容：Dataset.__getitem__返回结构与COCO格式完全一致
- 性能要求：数据加载性能不显著下降

---

### 迭代6：可视化验证工具

**目标**：提供格式转换正确性的可视化验证工具

**核心功能**：
- 独立的可视化验证脚本
- 批量处理和对比展示
- 转换正确性验证报告

**具体任务**：
1. 创建独立的可视化脚本visualize_conversion.py
2. 实现批量处理功能，支持完整数据集验证
3. 生成原始TableLabelMe标注与转换后格式的对比图像
4. 采用并排对比的展示方式，确保视觉效果一致
5. 生成转换正确性验证报告

**输入接口**：
- TableLabelMe数据集路径
- 可视化配置参数（输出目录、样本数量等）

**输出接口**：
- 可视化对比图像文件
- 转换正确性验证报告
- 异常样本统计和分析

**验收标准**：
- 视觉确认转换前后标注完全一致
- 批量处理功能稳定可靠
- 验证报告信息完整准确

**技术约束**：
- 独立性：不依赖训练环境，可独立运行
- 可扩展性：支持未来其他格式的可视化验证

## 3. 整体验收标准

### 3.1 功能完整性
- 所有需求功能都能正常工作
- 支持完整的训练和推理流程
- 多源数据配置功能正常

### 3.2 兼容性保证
- 原有COCO格式功能完全不受影响
- 所有现有训练和推理脚本正常工作
- 参数体系向后兼容

### 3.3 算法一致性
- 两种格式训练的模型性能一致
- 相同数据的训练结果可复现
- 推理结果完全一致

### 3.4 稳定性要求
- 异常情况下程序不崩溃
- 错误处理机制完善
- 日志记录完整准确

### 3.5 可维护性
- 代码结构清晰，易于理解
- 模块化设计，便于扩展
- 文档完整，使用简单

## 4. 技术交付物

### 4.1 核心代码模块
- TableLabelMe格式解析器
- 扩展的Dataset类
- 配置文件模板
- 可视化验证工具

### 4.2 配置文件
- configs/dataset_configs.py模板
- 参数配置示例
- 多源数据配置示例

### 4.3 验证工具
- 格式转换验证脚本
- 算法一致性测试脚本
- 性能对比测试工具

### 4.4 文档资料
- TableLabelMe格式支持使用指南
- 配置示例和命令行示例
- 故障排除指南

---

**文档版本**：v1.0  
**创建日期**：2025年1月21日  
**规划完成**：2025年1月21日
