#!/usr/bin/env bash

# 清理旧的编译文件
rm -rf build/ dist/ *.egg-info *.so

# 确保环境中有torch和CUDA
python -c "import torch; print('PyTorch version:', torch.__version__); print('CUDA available:', torch.cuda.is_available()); print('CUDA version:', torch.version.cuda if torch.cuda.is_available() else 'N/A')"

# 获取CUDA路径
if [ -z "$CUDA_HOME" ]; then
    if [ -d "/usr/local/cuda" ]; then
        export CUDA_HOME="/usr/local/cuda"
    else
        echo "CUDA_HOME not set and cannot find CUDA in /usr/local/cuda"
        echo "Please set CUDA_HOME manually"
        exit 1
    fi
fi
echo "Using CUDA from: $CUDA_HOME"

# 获取PyTorch库路径
TORCH_LIB_PATH=$(python -c "import torch; import os; print(os.path.dirname(torch.__file__))")
echo "PyTorch library path: $TORCH_LIB_PATH"

# 创建一个直接编译CUDA扩展的脚本
cat > compile_cuda_ext.py << EOF
import os
import glob
import torch
from torch.utils.cpp_extension import load, CUDA_HOME

# 确保CUDA可用
if not torch.cuda.is_available():
    raise RuntimeError("CUDA is not available. Cannot compile DCNv2 with GPU support.")

if CUDA_HOME is None:
    raise RuntimeError("CUDA_HOME is not set. Please set it manually.")

print(f"CUDA_HOME: {CUDA_HOME}")
print(f"torch.cuda.is_available(): {torch.cuda.is_available()}")

# 获取当前文件所在目录
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "src")

# 查找源文件
main_file = [os.path.join(src_dir, f) for f in os.listdir(src_dir) if f.endswith('.cpp')]
cpu_sources = [os.path.join(src_dir, 'cpu', f) for f in os.listdir(os.path.join(src_dir, 'cpu')) if f.endswith('.cpp')]
cuda_sources = [os.path.join(src_dir, 'cuda', f) for f in os.listdir(os.path.join(src_dir, 'cuda')) if f.endswith('.cu')]

print(f"Main sources: {main_file}")
print(f"CPU sources: {cpu_sources}")
print(f"CUDA sources: {cuda_sources}")

# 编译参数
cxx_args = ['-std=c++14']
nvcc_args = [
    '-DCUDA_HAS_FP16=1',
    '-D__CUDA_NO_HALF_OPERATORS__',
    '-D__CUDA_NO_HALF_CONVERSIONS__',
    '-D__CUDA_NO_HALF2_OPERATORS__',
    '-std=c++14',
]

# 获取PyTorch库路径，用于设置rpath
torch_lib_path = os.path.join(os.path.dirname(torch.__file__), 'lib')
print(f"Setting rpath to PyTorch library path: {torch_lib_path}")

# 明确定义宏以启用CUDA
define_macros = [("WITH_CUDA", None)]

# 动态编译扩展并保存到磁盘
ext_module = load(
    name='_ext',
    sources=main_file + cpu_sources + cuda_sources,
    extra_include_paths=[src_dir],
    extra_cflags=cxx_args,
    extra_cuda_cflags=nvcc_args,
    extra_ldflags=[f'-Wl,-rpath,{torch_lib_path}'],
    define_macros=define_macros,
    verbose=True,
    build_directory=current_dir
)

print("DCNv2 extension compiled successfully with CUDA support")
print("Module location:", ext_module.__file__)

# 验证CUDA函数是否可用
if hasattr(ext_module, 'dcn_v2_forward'):
    print("Module functions:", [f for f in dir(ext_module) if not f.startswith('__')])
    
    # 创建一个简单的测试来验证CUDA支持
    import torch
    input = torch.randn(2, 4, 8, 8).cuda()
    offset = torch.randn(2, 18, 8, 8).cuda()
    mask = torch.randn(2, 9, 8, 8).cuda()
    weight = torch.randn(4, 4, 3, 3).cuda()
    bias = torch.randn(4).cuda()
    
    try:
        output = ext_module.dcn_v2_forward(
            input, weight, bias, offset, mask, 
            3, 3, 1, 1, 1, 1, 1, 1, 1
        )
        print("CUDA test passed! DCNv2 is working correctly with GPU support.")
    except Exception as e:
        print(f"CUDA test failed: {e}")
else:
    print("ERROR: DCNv2 extension does not have expected functions")
EOF

# 编译扩展
python compile_cuda_ext.py

# 修改dcn_v2.py，确保它能正确处理CUDA
cat > dcn_v2_cuda_fix.py << 'EOF'
#!/usr/bin/env python
from __future__ import absolute_import, division, print_function

import math
import os
import torch
from torch import nn
from torch.autograd import Function
from torch.autograd.function import once_differentiable
from torch.nn.modules.utils import _pair

# 尝试导入预编译的 _ext 模块
try:
    import _ext as _backend
except ImportError as e:
    # 如果导入失败，尝试从当前目录加载
    try:
        import sys
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.append(current_dir)
        
        # 尝试设置LD_LIBRARY_PATH
        torch_lib_path = os.path.join(os.path.dirname(torch.__file__), 'lib')
        os.environ['LD_LIBRARY_PATH'] = f"{torch_lib_path}:{os.environ.get('LD_LIBRARY_PATH', '')}"
        
        import _ext as _backend
        print("Loaded _ext from current directory")
    except ImportError as e:
        # 如果仍然失败，尝试使用PyTorch内置的deform_conv2d
        print(f"WARNING: Could not import _ext module: {e}")
        print("Falling back to PyTorch's built-in deform_conv2d")
        _backend = None

# 如果有torchvision，尝试导入内置的deform_conv2d作为备选
try:
    from torchvision.ops import deform_conv2d as tv_deform_conv2d
    has_torchvision_deform = True
except ImportError:
    has_torchvision_deform = False


class _DCNv2(Function):
    @staticmethod
    def forward(
        ctx, input, offset, mask, weight, bias, stride, padding, dilation, deformable_groups
    ):
        # 确保输入在GPU上（如果使用GPU）
        ctx.stride = _pair(stride)
        ctx.padding = _pair(padding)
        ctx.dilation = _pair(dilation)
        ctx.kernel_size = _pair(weight.shape[2:4])
        ctx.deformable_groups = deformable_groups
        
        # 检查是否在GPU上
        if input.is_cuda:
            if _backend is None or not hasattr(_backend, 'dcn_v2_forward'):
                if has_torchvision_deform:
                    print("WARNING: Using torchvision's deform_conv2d as fallback for CUDA")
                    output = tv_deform_conv2d(
                        input, offset, weight, bias, 
                        stride=stride, padding=padding, dilation=dilation, mask=mask
                    )
                    ctx.save_for_backward(input, offset, mask, weight, bias)
                    return output
                else:
                    raise RuntimeError("DCNv2 CUDA implementation not available")
        
        # 调用后端实现
        output = _backend.dcn_v2_forward(
            input,
            weight,
            bias,
            offset,
            mask,
            ctx.kernel_size[0],
            ctx.kernel_size[1],
            ctx.stride[0],
            ctx.stride[1],
            ctx.padding[0],
            ctx.padding[1],
            ctx.dilation[0],
            ctx.dilation[1],
            ctx.deformable_groups,
        )
        ctx.save_for_backward(input, offset, mask, weight, bias)
        return output

    @staticmethod
    @once_differentiable
    def backward(ctx, grad_output):
        input, offset, mask, weight, bias = ctx.saved_tensors
        
        # 检查是否在GPU上
        if grad_output.is_cuda:
            if _backend is None or not hasattr(_backend, 'dcn_v2_backward'):
                raise RuntimeError("DCNv2 CUDA implementation not available for backward")
        
        grad_input, grad_offset, grad_mask, grad_weight, grad_bias = _backend.dcn_v2_backward(
            input,
            weight,
            bias,
            offset,
            mask,
            grad_output,
            ctx.kernel_size[0],
            ctx.kernel_size[1],
            ctx.stride[0],
            ctx.stride[1],
            ctx.padding[0],
            ctx.padding[1],
            ctx.dilation[0],
            ctx.dilation[1],
            ctx.deformable_groups,
        )

        return grad_input, grad_offset, grad_mask, grad_weight, grad_bias, None, None, None, None


dcn_v2_conv = _DCNv2.apply


class DCNv2(nn.Module):
    def __init__(
        self,
        in_channels,
        out_channels,
        kernel_size,
        stride,
        padding,
        dilation=1,
        deformable_groups=1,
    ):
        super(DCNv2, self).__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.kernel_size = _pair(kernel_size)
        self.stride = _pair(stride)
        self.padding = _pair(padding)
        self.dilation = _pair(dilation)
        self.deformable_groups = deformable_groups

        self.weight = nn.Parameter(torch.Tensor(out_channels, in_channels, *self.kernel_size))
        self.bias = nn.Parameter(torch.Tensor(out_channels))
        self.reset_parameters()

    def reset_parameters(self):
        n = self.in_channels
        for k in self.kernel_size:
            n *= k
        stdv = 1.0 / math.sqrt(n)
        self.weight.data.uniform_(-stdv, stdv)
        self.bias.data.zero_()

    def forward(self, input, offset, mask):
        assert (
            2 * self.deformable_groups * self.kernel_size[0] * self.kernel_size[1]
            == offset.shape[1]
        )
        assert self.deformable_groups * self.kernel_size[0] * self.kernel_size[1] == mask.shape[1]
        
        # 如果在GPU上但没有CUDA实现，尝试使用torchvision
        if input.is_cuda and (_backend is None or not hasattr(_backend, 'dcn_v2_forward')):
            if has_torchvision_deform:
                return tv_deform_conv2d(
                    input, 
                    offset, 
                    self.weight, 
                    self.bias, 
                    stride=self.stride,
                    padding=self.padding, 
                    dilation=self.dilation, 
                    mask=mask
                )
            else:
                raise RuntimeError("DCNv2 CUDA implementation not available and no fallback found")
        
        return dcn_v2_conv(
            input,
            offset,
            mask,
            self.weight,
            self.bias,
            self.stride,
            self.padding,
            self.dilation,
            self.deformable_groups,
        )


class DCN(DCNv2):
    def __init__(
        self,
        in_channels,
        out_channels,
        kernel_size,
        stride,
        padding,
        dilation=1,
        deformable_groups=1,
    ):
        super(DCN, self).__init__(
            in_channels, out_channels, kernel_size, stride, padding, dilation, deformable_groups
        )

        channels_ = self.deformable_groups * 3 * self.kernel_size[0] * self.kernel_size[1]
        self.conv_offset_mask = nn.Conv2d(
            self.in_channels,
            channels_,
            kernel_size=self.kernel_size,
            stride=self.stride,
            padding=self.padding,
            bias=True,
        )
        self.init_offset()

    def init_offset(self):
        self.conv_offset_mask.weight.data.zero_()
        self.conv_offset_mask.bias.data.zero_()

    def forward(self, input):
        out = self.conv_offset_mask(input)
        o1, o2, mask = torch.chunk(out, 3, dim=1)
        offset = torch.cat((o1, o2), dim=1)
        mask = torch.sigmoid(mask)
        
        # 如果在GPU上但没有CUDA实现，尝试使用torchvision
        if input.is_cuda and (_backend is None or not hasattr(_backend, 'dcn_v2_forward')):
            if has_torchvision_deform:
                return tv_deform_conv2d(
                    input, 
                    offset, 
                    self.weight, 
                    self.bias, 
                    stride=self.stride,
                    padding=self.padding, 
                    dilation=self.dilation, 
                    mask=mask
                )
            else:
                raise RuntimeError("DCNv2 CUDA implementation not available and no fallback found")
            
        return dcn_v2_conv(
            input,
            offset,
            mask,
            self.weight,
            self.bias,
            self.stride,
            self.padding,
            self.dilation,
            self.deformable_groups,
        )
EOF

# 备份原始文件
cp dcn_v2.py dcn_v2.py.bak

# 使用修复版本替换原始文件
cp dcn_v2_cuda_fix.py dcn_v2.py

# 创建一个CUDA测试脚本
cat > test_cuda.py << 'EOF'
import os
import torch
import sys

# 设置LD_LIBRARY_PATH
torch_lib_path = os.path.join(os.path.dirname(torch.__file__), 'lib')
os.environ['LD_LIBRARY_PATH'] = f"{torch_lib_path}:{os.environ.get('LD_LIBRARY_PATH', '')}"

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 检查CUDA是否可用
print(f"CUDA available: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"CUDA device: {torch.cuda.get_device_name(0)}")

# 尝试导入_ext模块
try:
    import _ext
    print("_ext module successfully imported")
    
    # 检查是否有CUDA函数
    has_cuda_funcs = hasattr(_ext, 'dcn_v2_forward')
    print(f"Has CUDA functions: {has_cuda_funcs}")
    
    # 测试CUDA功能
    if has_cuda_funcs and torch.cuda.is_available():
        # 创建测试张量
        input = torch.randn(2, 4, 8, 8).cuda()
        offset = torch.randn(2, 18, 8, 8).cuda()
        mask = torch.randn(2, 9, 8, 8).cuda()
        weight = torch.randn(4, 4, 3, 3).cuda()
        bias = torch.randn(4).cuda()
        
        try:
            output = _ext.dcn_v2_forward(
                input, weight, bias, offset, mask, 
                3, 3, 1, 1, 1, 1, 1, 1, 1
            )
            print("CUDA test passed! DCNv2 is working correctly with GPU support.")
        except Exception as e:
            print(f"CUDA test failed: {e}")
    
except ImportError as e:
    print(f"Error importing _ext: {e}")
    print("Current LD_LIBRARY_PATH:", os.environ.get('LD_LIBRARY_PATH', ''))

# 测试DCN模块
try:
    from dcn_v2 import DCN
    
    # 创建DCN模块
    dcn = DCN(4, 4, kernel_size=3, stride=1, padding=1)
    
    # 如果CUDA可用，将模块移到GPU
    if torch.cuda.is_available():
        dcn = dcn.cuda()
        
    # 创建输入
    input = torch.randn(2, 4, 8, 8)
    if torch.cuda.is_available():
        input = input.cuda()
    
    # 前向传播
    try:
        output = dcn(input)
        print("DCN forward pass successful!")
    except Exception as e:
        print(f"DCN forward pass failed: {e}")
        
except Exception as e:
    print(f"Error testing DCN: {e}")
EOF

# 运行CUDA测试
python test_cuda.py

# 创建环境设置脚本
cat > set_env.sh << EOF
#!/bin/bash
# 设置PyTorch库路径
export TORCH_LIB_PATH=\$(python -c "import torch; import os; print(os.path.join(os.path.dirname(torch.__file__), 'lib'))")
export LD_LIBRARY_PATH=\$TORCH_LIB_PATH:\$LD_LIBRARY_PATH

# 设置CUDA路径
if [ -d "/usr/local/cuda" ]; then
    export CUDA_HOME="/usr/local/cuda"
    export PATH=\$CUDA_HOME/bin:\$PATH
    export LD_LIBRARY_PATH=\$CUDA_HOME/lib64:\$LD_LIBRARY_PATH
fi

echo "Environment set up for DCNv2 with CUDA support."
echo "PyTorch library path: \$TORCH_LIB_PATH"
echo "CUDA_HOME: \$CUDA_HOME"
EOF

chmod +x set_env.sh

echo "Installation completed. DCNv2 should now be properly installed with CUDA support."
echo "IMPORTANT: Before running your scripts, set the correct environment variables:"
echo "  source $(pwd)/set_env.sh"