# LORE-TSR项目TableLabelMe数据格式支持 - 迭代二开发计划

## 项目概述

**当前迭代**: 迭代2 - 数据集扫描和索引构建  
**基于迭代**: 迭代1 MVP版本（已完成）  
**目标**: 实现完整TableLabelMe数据集的目录扫描和文件索引构建，将固定测试数据替换为真实的目录扫描机制

## 迭代二总体目标

根据PRD文档，迭代二的核心功能包括：
- 递归扫描part_xxxx目录结构
- 构建图像-标注文件映射关系  
- 生成全局唯一的image_id标识
- 支持多源数据路径配置
- 保持与迭代一完全兼容的接口

## 开发步骤详细计划

### 步骤2.1: 创建FileScanner核心模块

**当前迭代**: 迭代2 - 数据集扫描和索引构建  
**步骤目标**: 创建独立的文件扫描模块，实现基础的目录扫描功能

**影响文件**:
- `src/lib/datasets/parsers/file_scanner.py` [新增]

**具体操作**:
1. 创建FileScanner类，包含以下核心方法：
   - `__init__(config)`: 初始化扫描配置
   - `scan_directories(data_paths, split)`: 扫描多个数据源目录
   - `_scan_single_directory(root_path, dataset_source)`: 扫描单个数据源
   - `_find_part_directories(root_path)`: 查找part_xxxx目录
   - `_normalize_path(path)`: 路径规范化

2. 实现基础配置结构：
```python
scan_config = {
    "part_pattern": r"part_\d{4}",
    "image_extensions": [".jpg", ".jpeg", ".png"],
    "annotation_patterns": [".json", "_table_annotation.json"],
    "max_depth": 3,
    "follow_symlinks": False,
    "case_sensitive": False
}
```

3. 使用标准库os.walk实现目录遍历，避免复杂的并发机制

**受影响的现有模块**: 无（独立模块）

**复用已有代码**: 
- 复用迭代1中的错误处理模式
- 参考现有数据集类的日志记录方式

**如何验证**:
```bash
# 创建测试脚本验证FileScanner基础功能
python -c "
from src.lib.datasets.parsers.file_scanner import FileScanner
scanner = FileScanner()
print('FileScanner创建成功')
print('基础方法存在:', hasattr(scanner, 'scan_directories'))
"
```

### 步骤2.2: 实现文件匹配和ID生成算法

**当前迭代**: 迭代2 - 数据集扫描和索引构建  
**步骤目标**: 完善FileScanner的核心算法，实现文件匹配和唯一ID生成

**影响文件**:
- `src/lib/datasets/parsers/file_scanner.py` [修改]

**具体操作**:
1. 实现`_match_image_annotation_pairs(files)`方法：
   - 按文件名前缀分组图像和标注文件
   - 支持.json和_table_annotation.json两种命名模式
   - 记录孤儿文件统计信息

2. 实现`_generate_image_id(file_path)`方法：
   - 使用MD5哈希算法处理规范化路径
   - 确保相同路径始终生成相同ID
   - 转换为64位整数ID

3. 实现`_collect_statistics(scan_results)`方法：
   - 收集详细的扫描统计信息
   - 包含目录数、文件数、匹配对数等

4. 添加文件验证方法`_validate_file_pair(image_path, annotation_path)`

**受影响的现有模块**: 无

**复用已有代码**:
- 参考迭代1中TableLabelMeParser的文件处理逻辑
- 复用现有的路径处理工具函数

**如何验证**:
```bash
# 创建测试目录结构和测试脚本
mkdir -p test_data/part_0001
echo "test" > test_data/part_0001/test.jpg
echo "{}" > test_data/part_0001/test.json

python -c "
from src.lib.datasets.parsers.file_scanner import FileScanner
scanner = FileScanner()
result = scanner.scan_directories(['test_data'], 'train')
print('扫描结果:', len(result))
print('ID生成测试通过')
"
```

### 步骤2.3: 集成FileScanner到解析器包

**当前迭代**: 迭代2 - 数据集扫描和索引构建  
**步骤目标**: 将FileScanner集成到解析器包的导入体系中

**影响文件**:
- `src/lib/datasets/parsers/__init__.py` [修改]

**具体操作**:
1. 在__init__.py中添加FileScanner的导入：
```python
from .file_scanner import FileScanner
__all__ = ['BaseParser', 'TableLabelMeParser', 'FileScanner']
```

2. 确保导入路径正确，不影响现有导入

**受影响的现有模块**: 
- 解析器包的导入体系
- 依赖解析器包的其他模块

**复用已有代码**:
- 遵循现有解析器包的导入模式
- 保持与BaseParser和TableLabelMeParser一致的导入风格

**如何验证**:
```bash
# 验证导入功能正常
python -c "
from src.lib.datasets.parsers import FileScanner, BaseParser, TableLabelMeParser
print('所有解析器导入成功')
print('FileScanner类型:', type(FileScanner))
"
```

### 步骤2.4: 修改TableLabelMe数据集类集成真实扫描

**当前迭代**: 迭代2 - 数据集扫描和索引构建
**步骤目标**: 修改TableLabelMe数据集类，将固定测试数据替换为真实的目录扫描机制

**影响文件**:
- `src/lib/datasets/dataset/table_labelmev2.py` [修改]

**具体操作**:
1. 修改`__init__`方法，集成FileScanner：
```python
from ..parsers import FileScanner

def __init__(self, opt, split):
    super(TableLabelMeDataset, self).__init__()
    self.opt = opt
    self.split = split
    self.parser = TableLabelMeParser()

    # 新增：创建文件扫描器
    self.file_scanner = FileScanner()

    # 其他初始化保持不变
    self.file_index = self._build_file_index()
    self._load_annotations()
```

2. 重写`_build_file_index`方法：
```python
def _build_file_index(self):
    """构建真实的文件索引，替换固定测试数据"""
    # 获取数据路径配置（当前使用硬编码，迭代4将支持外部配置）
    data_paths = self._get_data_paths()

    # 使用FileScanner进行目录扫描
    scan_result = self.file_scanner.scan_directories(data_paths, self.split)

    # 保存扫描统计信息
    self.scan_statistics = scan_result.get('statistics', {})

    # 返回文件索引（格式与迭代1兼容）
    return scan_result.get('file_index', {})
```

3. 添加`_get_data_paths`方法（硬编码版本，为迭代4预留接口）：
```python
def _get_data_paths(self):
    """获取数据路径配置（迭代2硬编码版本）"""
    # 这里使用硬编码路径，迭代4将支持外部配置文件
    base_paths = {
        'train': ['/path/to/test/data/train'],  # 测试路径
        'val': ['/path/to/test/data/val']
    }
    return base_paths.get(self.split, [])
```

**受影响的现有模块**:
- TableLabelMe数据集的初始化流程
- 数据加载和索引构建流程
- 与数据集工厂的集成

**复用已有代码**:
- 保持现有的_load_annotations方法不变
- 复用现有的数据预处理流程
- 保持与COCO API兼容的接口设计

**如何验证**:
```bash
# 创建测试数据结构
mkdir -p test_data/train/part_0001 test_data/val/part_0001
echo "test" > test_data/train/part_0001/test.jpg
echo '{"quality": "合格", "cells": []}' > test_data/train/part_0001/test.json

# 验证数据集可以加载真实数据
python -c "
import sys
sys.path.append('src')
from lib.datasets.dataset_factory import get_dataset

# 创建数据集类
Dataset = get_dataset('table', 'ctdet')

# 模拟配置对象
class MockOpt:
    def __init__(self):
        self.data_dir = 'test_data'
        self.dataset_name = 'TableLabelMe'

opt = MockOpt()
dataset = Dataset(opt, 'train')
print('数据集创建成功，样本数量:', len(dataset))
print('扫描统计:', getattr(dataset, 'scan_statistics', {}))
"
```

### 步骤2.5: 端到端集成测试和验证

**当前迭代**: 迭代2 - 数据集扫描和索引构建
**步骤目标**: 创建迭代二的集成测试，验证完整的目录扫描和数据加载流程

**影响文件**:
- `test_tablelabelme_iter2_integration.py` [新增]

**具体操作**:
1. 创建迭代二专用的集成测试脚本：
```python
#!/usr/bin/env python3
"""
TableLabelMe格式支持集成测试 - 迭代2
验证目录扫描和文件索引构建功能
"""

def test_file_scanner():
    """测试FileScanner独立功能"""
    print("=== 测试FileScanner模块 ===")
    # 测试目录扫描、文件匹配、ID生成等功能

def test_dataset_integration():
    """测试数据集集成功能"""
    print("=== 测试数据集集成 ===")
    # 测试TableLabelMe数据集的真实数据加载

def test_compatibility():
    """测试与迭代1的兼容性"""
    print("=== 测试向后兼容性 ===")
    # 确保迭代1的所有功能仍然正常

def test_end_to_end():
    """测试端到端流程"""
    print("=== 测试端到端流程 ===")
    # 测试完整的训练数据加载流程
```

2. 验证与迭代1的完全兼容性
3. 测试多源数据路径的合并功能
4. 验证扫描性能和内存使用

**受影响的现有模块**:
- 整个TableLabelMe数据支持体系
- 数据集工厂和配置系统

**复用已有代码**:
- 复用迭代1的测试框架和验证逻辑
- 扩展现有的集成测试模式

**如何验证**:
```bash
# 运行迭代二集成测试
python test_tablelabelme_iter2_integration.py

# 验证完整训练流程（使用测试数据）
python src/main.py ctdet \
    --dataset table \
    --dataset_name TableLabelMe \
    --data_config test_config \
    --num_epochs 1 \
    --batch_size 1 \
    --test

# 验证与迭代1的兼容性
python test_tablelabelme_integration.py  # 迭代1的测试应该仍然通过
```

## 迭代二逻辑流程图

```mermaid
sequenceDiagram
    participant Main as main.py
    participant TLD as TableLabelMeDataset
    participant FS as FileScanner
    participant TLP as TableLabelMeParser

    Main->>TLD: 创建数据集实例
    TLD->>TLD: __init__(opt, split)
    TLD->>FS: 创建FileScanner实例

    TLD->>TLD: _build_file_index()
    TLD->>TLD: _get_data_paths()
    TLD->>FS: scan_directories(data_paths, split)

    FS->>FS: 递归扫描part_xxxx目录
    FS->>FS: _find_part_directories()
    FS->>FS: _match_image_annotation_pairs()
    FS->>FS: _generate_image_id()
    FS->>FS: _collect_statistics()
    FS-->>TLD: 返回文件索引和统计信息

    TLD->>TLD: _load_annotations()
    loop 每个文件对
        TLD->>TLP: parse_file(json_path, image_path)
        TLP-->>TLD: 标准化数据字典
    end

    Main->>TLD: dataset[index]
    TLD-->>Main: 与COCO格式兼容的训练数据
```

## 验收标准

### 功能验收
- [ ] FileScanner可以正确扫描part_xxxx目录结构
- [ ] 文件匹配算法正确识别图像-标注文件对
- [ ] image_id生成具有稳定性和唯一性
- [ ] TableLabelMe数据集可以加载真实数据
- [ ] 与迭代1完全兼容，所有现有测试通过

### 性能验收
- [ ] 目录扫描性能合理（1000文件 < 10秒）
- [ ] 内存使用控制在合理范围内
- [ ] 不显著影响数据加载速度

### 质量验收
- [ ] 代码模块化良好，FileScanner < 400行
- [ ] 错误处理机制完善
- [ ] 日志记录清晰准确
- [ ] 接口设计向后兼容

## 技术约束

1. **兼容性约束**: 必须保持与迭代1的完全兼容
2. **性能约束**: 不显著影响现有数据加载性能
3. **代码约束**: 使用标准库，避免引入额外依赖
4. **接口约束**: _build_file_index返回格式必须兼容

## 后续迭代预留

- **迭代3**: 质量筛选功能通过FileScanner的验证接口扩展
- **迭代4**: 外部配置文件支持通过_get_data_paths方法替换
- **迭代5**: 训练流程集成通过现有数据集接口无缝支持

---

**文档版本**: v1.0
**创建日期**: 2025年1月21日
**迭代范围**: 迭代2 - 数据集扫描和索引构建
**依赖**: 基于迭代1 MVP版本的完整实现
**状态**: 待用户确认后开始执行
